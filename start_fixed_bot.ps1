# BYBIT TRADING BOT - SINGLE ENTRY POINT ONLY
# PowerShell script - ONLY main.py allowed as entry point

Write-Host "================================================" -ForegroundColor Green
Write-Host "BYBIT TRADING BOT - SINGLE ENTRY POINT ONLY" -ForegroundColor Green
Write-Host "USING ONLY main.py - ALL OTHER ENTRY POINTS DELETED" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green
Write-Host ""

# Change to bot directory
Set-Location "e:\The_real_deal_copy\Bybit_Bot\BOT"

Write-Host "Cleaning up obsolete entry points first..." -ForegroundColor Yellow
try {
    $result = & conda run --live-stream --name bybit-trader python emergency_cleanup_entry_points.py
    Write-Host $result
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Could not clean up obsolete entry points" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
} catch {
    Write-Host "ERROR: Could not run cleanup: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow
Write-Host ""

Write-Host "Verifying critical fixes..." -ForegroundColor Cyan
try {
    $result = & conda run --live-stream --name bybit-trader python verify_fixes_direct.py
    Write-Host $result
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Critical fixes verification failed" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
} catch {
    Write-Host "ERROR: Could not run verification: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "================================================" -ForegroundColor Green
Write-Host "STARTING BYBIT TRADING BOT USING ONLY main.py" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green
Write-Host ""

Write-Host "Python environment info:" -ForegroundColor Yellow
try {
    & conda run --live-stream --name bybit-trader python --version
} catch {
    Write-Host "WARNING: Could not get Python version" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Starting SINGLE ENTRY POINT: main.py" -ForegroundColor Cyan

try {
    & conda run --live-stream --name bybit-trader python main.py
} catch {
    Write-Host "ERROR: Failed to start bot: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "Bot execution completed." -ForegroundColor Green
Read-Host "Press Enter to exit"
