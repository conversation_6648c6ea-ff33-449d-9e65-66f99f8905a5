@echo off
REM Fix MCP Connection Issues for VS Code
echo [INFO] Fixing MCP connection issues...

REM Activate conda environment
call E:\Miniconda\Scripts\activate.bat bybit-trader

REM Install missing packages for MCP servers
echo [INFO] Installing UV package manager...
pip install uv

echo [INFO] Installing MCP packages globally...
npm install -g @upstash/context7-mcp@latest
npm install -g @modelcontextprotocol/server-sequential-thinking@latest

REM Create symbolic links for uvx command
echo [INFO] Creating uvx alias...
if not exist "E:\conda\envs\bybit-trader\Scripts\uvx.cmd" (
    echo @echo off > "E:\conda\envs\bybit-trader\Scripts\uvx.cmd"
    echo uv %%* >> "E:\conda\envs\bybit-trader\Scripts\uvx.cmd"
)

REM Test MCP server connections
echo [INFO] Testing MCP server connections...
echo Testing Context7 MCP server...
npx @upstash/context7-mcp@latest --version 2>nul
if %errorlevel% equ 0 (
    echo [OK] Context7 MCP server accessible
) else (
    echo [WARNING] Context7 MCP server not accessible
)

echo Testing Sequential Thinking MCP server...
npx @modelcontextprotocol/server-sequential-thinking@latest --version 2>nul
if %errorlevel% equ 0 (
    echo [OK] Sequential Thinking MCP server accessible
) else (
    echo [WARNING] Sequential Thinking MCP server not accessible
)

echo [INFO] MCP fix script completed.
echo [INFO] Please restart VS Code for changes to take effect.
pause
