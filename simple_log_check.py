#!/usr/bin/env python3
"""
Simple Log Check - Direct file access
"""

import os
from pathlib import Path

def main():
    log_file = Path('logs/bybit_trading_bot.log')
    
    if not log_file.exists():
        print("Log file not found")
        return
    
    try:
        # Get file size
        file_size = log_file.stat().st_size
        print(f"Log file size: {file_size} bytes")
        
        # Read file
        with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        lines = content.split('\n')
        print(f"Total lines: {len(lines)}")
        
        # Check for bids errors
        bids_errors = [line for line in lines if "'bids'" in line and "Error getting ultra-fast price data" in line]
        print(f"Total bids errors: {len(bids_errors)}")
        
        # Check recent lines (last 100)
        recent_lines = lines[-100:] if len(lines) >= 100 else lines
        recent_bids = [line for line in recent_lines if "'bids'" in line and "Error getting ultra-fast price data" in line]
        print(f"Recent bids errors (last 100 lines): {len(recent_bids)}")
        
        # Show last few lines
        print("\nLast 5 lines:")
        for line in lines[-5:]:
            if line.strip():
                print(f"  {line.strip()}")
        
        # Status
        if len(recent_bids) == 0:
            print("\nSTATUS: NO RECENT BIDS ERRORS - FIX IS WORKING!")
        else:
            print(f"\nSTATUS: {len(recent_bids)} RECENT BIDS ERRORS FOUND")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
