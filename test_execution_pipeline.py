#!/usr/bin/env python3
"""
Test the complete execution pipeline to verify opportunities are being executed
"""

import asyncio
import sys
sys.path.insert(0, '.')

from dotenv import load_dotenv
from bybit_bot.core.config import EnhancedBotConfig
from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
from bybit_bot.profit_maximization.advanced_profit_engine import AdvancedProfitEngine
from bybit_bot.database.connection import DatabaseManager

load_dotenv()

async def test_execution_pipeline():
    """Test the complete execution pipeline"""
    print("TESTING COMPLETE EXECUTION PIPELINE")
    print("=" * 50)
    
    try:
        # Initialize components
        config = EnhancedBotConfig()
        bybit_client = EnhancedBybitClient(config)
        await bybit_client.initialize()
        
        db_manager = DatabaseManager(config)
        await db_manager.initialize()
        
        # Create profit engine
        profit_engine = AdvancedProfitEngine(config, bybit_client, db_manager)
        
        print("1. TESTING OPPORTUNITY DETECTION AND SCORING:")
        
        # Test with BTCUSDT
        symbol = "BTCUSDT"
        price_data = await profit_engine._get_ultra_fast_price_data(symbol)
        
        if price_data:
            opportunity = await profit_engine._detect_scalping_opportunity(symbol, price_data)
            if opportunity:
                print(f"   OPPORTUNITY FOUND:")
                print(f"   - Expected Profit: ${opportunity.expected_profit:.6f}")
                print(f"   - Confidence: {opportunity.confidence:.2f}")
                print(f"   - Risk Score: {opportunity.risk_score:.2f}")
                
                # Calculate profit score
                profit_score = await profit_engine._calculate_profit_score(opportunity)
                print(f"   - Profit Score: {profit_score:.6f}")
                
                # Check if it passes the threshold
                if profit_score > 0.001:
                    print(f"   SUCCESS: Profit score {profit_score:.6f} > 0.001 threshold")
                    print(f"   RESULT: Opportunity WILL be added to execution queue")
                else:
                    print(f"   FAILURE: Profit score {profit_score:.6f} <= 0.001 threshold")
                    print(f"   RESULT: Opportunity will be REJECTED")
                    
                # Test validation
                is_valid = await profit_engine._validate_opportunity(opportunity)
                print(f"   - Validation: {'PASSED' if is_valid else 'FAILED'}")
                
            else:
                print("   NO OPPORTUNITY FOUND")
        else:
            print("   ERROR: Could not get price data")
            
        print("\n2. TESTING EXECUTION QUEUE PROCESSING:")
        
        # Manually add an opportunity to test execution
        if 'opportunity' in locals() and opportunity:
            print("   Adding opportunity to execution queue...")
            await profit_engine.execution_queue.put(opportunity)
            queue_size = profit_engine.execution_queue.qsize()
            print(f"   Execution queue size: {queue_size}")
            
            if queue_size > 0:
                print("   SUCCESS: Opportunity added to execution queue")
                
                # Test execution (but don't actually place order)
                print("   Testing execution logic...")
                try:
                    # Get the opportunity back
                    test_opportunity = await asyncio.wait_for(
                        profit_engine.execution_queue.get(), 
                        timeout=1.0
                    )
                    print(f"   Retrieved opportunity: {test_opportunity.symbol}")
                    print(f"   Strategy: {test_opportunity.strategy}")
                    print(f"   Quantity: {test_opportunity.quantity}")
                    
                    # Test order parameters
                    side = "buy" if test_opportunity.entry_price < test_opportunity.target_price else "sell"
                    print(f"   Order side: {side}")
                    print(f"   Order type: Market")
                    print(f"   Entry price: ${test_opportunity.entry_price:.2f}")
                    print(f"   Target price: ${test_opportunity.target_price:.2f}")
                    
                    print("   SUCCESS: Execution logic working correctly")
                    
                except asyncio.TimeoutError:
                    print("   ERROR: Could not retrieve opportunity from queue")
                    
            else:
                print("   ERROR: Opportunity not added to queue")
                
        print("\n3. SUMMARY:")
        print("   Pipeline test completed")
        print("   If profit score > 0.001 and validation passes,")
        print("   opportunities should now be executed as market orders")
        
        await bybit_client.close()
        await db_manager.close()
        
    except Exception as e:
        print(f"ERROR in test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_execution_pipeline())
