#!/usr/bin/env python3
"""
Fix ALL remaining Pylance errors across the entire codebase
AUTOMATED_MANUAL.md Section 0.2 - Zero tolerance for Pylance errors
"""

import os
import re
from pathlib import Path

def fix_trading_signal_constructors():
    """Fix all TradingSignal constructor calls"""
    print("FIXING TRADING SIGNAL CONSTRUCTORS...")
    
    learning_agent_path = Path("bybit_bot/agents/learning_agent.py")
    
    try:
        content = learning_agent_path.read_text(encoding='utf-8')
        
        # Fix all TradingSignal constructor calls
        # Remove unsupported parameters and fix parameter names
        
        # Replace 'reasoning' with 'reason'
        content = re.sub(r'reasoning=', r'reason=', content)
        
        # Remove unsupported parameters completely
        unsupported_params = [
            r'expected_return=[^,\n)]*,?\s*',
            r'position_size=[^,\n)]*,?\s*'
        ]
        
        for param_pattern in unsupported_params:
            content = re.sub(param_pattern, '', content)
        
        # Fix specific TradingSignal constructor calls
        # Pattern 1: Fix margin arbitrage signal
        content = re.sub(
            r'return TradingSignal\(\s*symbol=symbol,\s*action=action,\s*confidence=confidence,.*?\)',
            '''return TradingSignal(
                    symbol=symbol,
                    action=action,
                    strength=confidence,
                    confidence=confidence,
                    strategy=StrategyType.MARGIN_ARBITRAGE.value,
                    entry_price=current_price,
                    stop_loss=current_price * (0.99 if action == 'buy' else 1.01),
                    take_profit=current_price * (1.02 if action == 'buy' else 0.98),
                    reason=f"Memory-enhanced funding arbitrage: {funding_rate:.4f} rate, {max_leverage}x leverage",
                    timestamp=datetime.now(timezone.utc),
                    risk_score=1.0 - confidence,
                    expected_holding_time=60,
                    sentiment_score=None,
                    news_impact=None,
                    technical_score=confidence,
                    ml_prediction={'leverage': max_leverage, 'funding_rate': funding_rate}
                )''',
            content,
            flags=re.DOTALL
        )
        
        learning_agent_path.write_text(content, encoding='utf-8')
        print("  FIXED: TradingSignal constructors in learning_agent.py")
        
    except Exception as e:
        print(f"ERROR: Could not fix TradingSignal constructors: {e}")

def fix_missing_imports():
    """Fix missing imports across all files"""
    print("FIXING MISSING IMPORTS...")
    
    files_to_fix = [
        "bybit_bot/agents/learning_agent.py",
        "bybit_bot/agents/agent_orchestrator.py",
        "bybit_bot/strategies/trend_following_strategy.py"
    ]
    
    for file_path in files_to_fix:
        try:
            path = Path(file_path)
            if not path.exists():
                continue
                
            content = path.read_text(encoding='utf-8')
            
            # Add missing imports
            if file_path == "bybit_bot/agents/learning_agent.py":
                if "from pathlib import Path" not in content:
                    content = content.replace(
                        "from datetime import datetime, timedelta, timezone",
                        "from datetime import datetime, timedelta, timezone\nfrom pathlib import Path"
                    )
                
                # Fix type annotations
                content = re.sub(r'success: bool = field\(default=False\)', r'success: bool = field(default=False)', content)
                
                # Fix float return type issues
                content = re.sub(r'return float\(np\.mean\([^)]+\)\)', r'return float(np.mean(spacing))', content)
                
                # Fix leverage type issues
                content = re.sub(r'scalp_leverage = min\(scalp_leverage \* 1\.2, 100\)', 
                               r'scalp_leverage = int(min(scalp_leverage * 1.2, 100))', content)
                content = re.sub(r'scalp_leverage = max\(scalp_leverage \* 0\.5, 5\)', 
                               r'scalp_leverage = int(max(scalp_leverage * 0.5, 5))', content)
                content = re.sub(r'portfolio_leverage = min\(portfolio_leverage \* 1\.25, self\.max_leverage\.get\(symbol, 50\)\)', 
                               r'portfolio_leverage = int(min(portfolio_leverage * 1.25, self.max_leverage.get(symbol, 50)))', content)
                content = re.sub(r'portfolio_leverage = max\(portfolio_leverage \* 0\.7, 1\)', 
                               r'portfolio_leverage = int(max(portfolio_leverage * 0.7, 1))', content)
                
                # Fix symbol_counts type annotation
                content = re.sub(r'symbol_counts = \{\}', r'symbol_counts: Dict[str, int] = {}', content)
                
                # Fix return type issues
                content = re.sub(r'return volatility', r'return float(volatility)', content)
                
                # Fix best_score type
                content = re.sub(r'best_score = score', r'best_score = float(score)', content)
                
                # Fix confidence type
                content = re.sub(r"strategy_votes\[strategy\]\['confidence'\] \+= confidence", 
                               r"strategy_votes[strategy]['confidence'] += float(confidence)", content)
            
            elif file_path == "bybit_bot/agents/agent_orchestrator.py":
                # Remove unused imports
                content = re.sub(r'from dataclasses import dataclass, asdict, field', 
                               r'from dataclasses import dataclass, field', content)
                content = re.sub(r'import json\n', '', content)
                
                # Remove unused variables
                content = re.sub(r'status = await agent\.get_status\(\)\s*\n\s*self\.agent_info', 
                               r'await agent.get_status()\n                        self.agent_info', content)
                content = re.sub(r'result = message\.data\[\'result\'\]\s*\n', '', content)
                content = re.sub(r'for agent_type, info in self\.agent_info\.items\(\):', 
                               r'for _, info in self.agent_info.items():', content)
            
            elif file_path == "bybit_bot/strategies/trend_following_strategy.py":
                # Remove unused imports
                content = re.sub(r'import asyncio\n', '', content)
                content = re.sub(r', timedelta', '', content)
                content = re.sub(r', List', '', content)
                
                # Fix pandas operations
                content = re.sub(r'plus_dm = np\.where\(\(plus_dm > minus_dm\) & \(plus_dm > 0\), plus_dm, 0\)',
                               r'plus_dm = np.where((plus_dm > minus_dm) & (plus_dm > 0), plus_dm, 0)', content)
                content = re.sub(r'minus_dm = np\.where\(\(minus_dm > plus_dm\) & \(minus_dm > 0\), minus_dm, 0\)',
                               r'minus_dm = np.where((minus_dm > plus_dm) & (minus_dm > 0), minus_dm, 0)', content)
                
                # Fix ADX calculation
                content = re.sub(r'adx = dx\.rolling\(window=period\)\.mean\(\)',
                               r'adx = pd.Series(dx).rolling(window=period).mean()', content)
                
                # Fix return type issues
                content = re.sub(r'return min\(1\.0, np\.mean\(spacing\) \* 10\)',
                               r'return float(min(1.0, np.mean(spacing) * 10))', content)
                content = re.sub(r'return max\(-1\.0, -np\.mean\(spacing\) \* 10\)',
                               r'return float(max(-1.0, -np.mean(spacing) * 10))', content)
                
                # Fix undefined variable
                content = re.sub(r'"entry_price": df\[\'close\'\]\.iloc\[-1\]',
                               r'"entry_price": close.iloc[-1]', content)
                
                # Remove unused parameters
                content = re.sub(r'def _calculate_stop_loss\(self, signal: str, indicators',
                               r'def _calculate_stop_loss(self, indicators', content)
                content = re.sub(r'def _calculate_take_profit\(self, signal: str, indicators',
                               r'def _calculate_take_profit(self, indicators', content)
                content = re.sub(r'self\._calculate_stop_loss\(base_signal, indicators',
                               r'self._calculate_stop_loss(indicators', content)
                content = re.sub(r'self\._calculate_take_profit\(base_signal, indicators',
                               r'self._calculate_take_profit(indicators', content)
                
                # Remove unused variable
                content = re.sub(r'signal = \{[^}]*\}\s*\n\s*return \{', r'return {', content)
            
            path.write_text(content, encoding='utf-8')
            print(f"  FIXED: Imports and types in {file_path}")
            
        except Exception as e:
            print(f"ERROR: Could not fix {file_path}: {e}")

def fix_init_file():
    """Fix __init__.py __all__ issues"""
    print("FIXING __INIT__.PY...")
    
    init_path = Path("bybit_bot/__init__.py")
    
    try:
        content = init_path.read_text(encoding='utf-8')
        
        # Fix __all__ to be empty since we're using dynamic imports
        content = re.sub(
            r'__all__: list\[str\] = \[\s*# Core modules will be dynamically imported to avoid circular dependencies\s*\]',
            r'__all__: list[str] = []',
            content
        )
        
        init_path.write_text(content, encoding='utf-8')
        print("  FIXED: __init__.py __all__ declaration")
        
    except Exception as e:
        print(f"ERROR: Could not fix __init__.py: {e}")

if __name__ == "__main__":
    fix_trading_signal_constructors()
    fix_missing_imports()
    fix_init_file()
    print("COMPLETED: All Pylance error fixes applied")
