"""
AI FOLDER ACTIVATION MANAGER - COMPREHENSIVE AI SYSTEM ACTIVATION
Ensures all AI components in the bybit_bot/ai folder are properly activated and integrated

MANDATORY ACTIVATION LIST:
- <PERSON><PERSON> Learner: Continuous learning and adaptation
- Model Selector: Optimal model selection and switching
- Recursive Improvement System: Self-improving algorithms
- Memory Manager: Advanced pattern memory and recall
- Meta Cognition Engine: Self-awareness and reflection
- OpenRouter Client: External AI model integration
- Self-Correcting Code Evolution: Autonomous code improvement
- SuperGPT Integration: Advanced reasoning capabilities
- Intelligent ML System: Machine learning orchestration
- Advanced Market Predictor: Market prediction algorithms
- Advanced Risk Manager: AI-enhanced risk management
- Advanced Memory System: Comprehensive memory architecture

ACTIVATION REQUIREMENTS:
- ALL AI systems must remain permanently active
- NO AI function can be disabled or simplified
- Continuous monitoring and auto-reactivation
- Maximum performance optimization
- Autonomous operation without manual intervention
"""

import asyncio
import logging
import importlib
import inspect
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
import sys
import os

# Add the AI folder to Python path
AI_FOLDER_PATH = Path(__file__).parent
if str(AI_FOLDER_PATH) not in sys.path:
    sys.path.insert(0, str(AI_FOLDER_PATH))


@dataclass
class AIComponentStatus:
    """Status tracking for AI components"""
    name: str
    module_path: str
    is_active: bool = False
    last_activation: Optional[datetime] = None
    activation_count: int = 0
    error_count: int = 0
    last_error: Optional[str] = None
    performance_score: float = 0.0
    instance: Optional[Any] = None


class SimpleMemoryManager:
    """Simple memory manager for components that need basic memory functionality"""
    def __init__(self):
        self.memory = {}
    
    def store(self, key: str, value: Any):
        self.memory[key] = value
    
    def retrieve(self, key: str):
        return self.memory.get(key)
    
    def clear(self):
        self.memory.clear()


class AIFolderActivationManager:
    """Comprehensive AI Folder Activation and Management System"""
    
    def __init__(self, config=None, database_manager=None, bybit_client=None):
        self.logger = logging.getLogger("ai_folder_activation")
        self.ai_components: Dict[str, AIComponentStatus] = {}
        
        # Dependency injection
        self.config = config
        self.database_manager = database_manager
        self.bybit_client = bybit_client
        
        self.activation_order = [
            'meta_cognition_engine',
            'meta_learner', 
            'memory_manager',
            'advanced_memory_system',
            'model_selector',
            'intelligent_ml_system',
            'recursive_improvement_system',
            'advanced_market_predictor',
            'advanced_risk_manager',
            'openrouter_client',
            'self_correcting_code_evolution',
            'supergpt_integration'
        ]
        
        self.mandatory_ai_systems = {
            'meta_learner': 'bybit_bot.ai.meta_learner',
            'model_selector': 'bybit_bot.ai.model_selector',
            'recursive_improvement_system': 'bybit_bot.ai.recursive_improvement_system',
            'memory_manager': 'bybit_bot.ai.memory_manager',
            'meta_cognition_engine': 'bybit_bot.ai.meta_cognition_engine',
            'openrouter_client': 'bybit_bot.ai.openrouter_client',
            'self_correcting_code_evolution': 'bybit_bot.ai.self_correcting_code_evolution',
            'supergpt_integration': 'bybit_bot.ai.supergpt_integration',
            'intelligent_ml_system': 'bybit_bot.ai.intelligent_ml_system',
            'advanced_market_predictor': 'bybit_bot.ai.advanced_market_predictor',
            'advanced_risk_manager': 'bybit_bot.ai.advanced_risk_manager',
            'advanced_memory_system': 'bybit_bot.ai.advanced_memory_system'
        }
        
        self.active_instances = {}
        self.activation_callbacks = []
        self.monitoring_active = False
        
        self.logger.info("AI Folder Activation Manager initialized - MANDATORY ACTIVATION MODE")
    
    async def activate_all_ai_systems(self) -> Dict[str, bool]:
        """Activate ALL AI systems in the mandatory activation order"""
        self.logger.info("Starting MANDATORY AI SYSTEM ACTIVATION - ALL SYSTEMS MUST BE ACTIVE")
        activation_results = {}
        
        for component_name in self.activation_order:
            if component_name in self.mandatory_ai_systems:
                module_path = self.mandatory_ai_systems[component_name]
                result = await self._activate_ai_component(component_name, module_path)
                activation_results[component_name] = result
                
                if not result:
                    self.logger.error(f"CRITICAL: Failed to activate {component_name} - MANDATORY SYSTEM")
                else:
                    self.logger.info(f"SUCCESS: {component_name} activated and operational")
        
        # Verify all systems are active
        total_systems = len(self.mandatory_ai_systems)
        active_systems = sum(1 for status in activation_results.values() if status)
        
        self.logger.info(f"AI ACTIVATION COMPLETE: {active_systems}/{total_systems} systems active")
        
        if active_systems < total_systems:
            self.logger.warning(f"WARNING: {total_systems - active_systems} AI systems failed to activate")
            await self._attempt_failed_system_recovery()
        
        return activation_results
    
    async def _activate_ai_component(self, component_name: str, module_path: str) -> bool:
        """Activate a specific AI component with proper dependency injection"""
        try:
            self.logger.info(f"Activating AI component: {component_name}")
            
            # Initialize component status
            if component_name not in self.ai_components:
                self.ai_components[component_name] = AIComponentStatus(
                    name=component_name,
                    module_path=module_path
                )
            
            component_status = self.ai_components[component_name]
            
            # Import the module
            module = importlib.import_module(module_path)
            
            # Find the main class in the module
            main_class = self._find_main_class(module, component_name)
            
            if main_class:
                # Create instance with proper dependencies
                instance = self._create_instance_with_dependencies(main_class, component_name)
                
                if instance is None:
                    return False
                
                # Initialize if has init method
                if hasattr(instance, 'initialize') and callable(getattr(instance, 'initialize')):
                    try:
                        if inspect.iscoroutinefunction(instance.initialize):
                            await instance.initialize()
                        else:
                            instance.initialize()
                    except Exception as init_error:
                        self.logger.warning(f"Initialize method failed for {component_name}: {init_error}")
                        # Continue anyway - some components may not need initialization
                
                # Start if has start method
                if hasattr(instance, 'start') and callable(getattr(instance, 'start')):
                    try:
                        if inspect.iscoroutinefunction(instance.start):
                            # Start in background task
                            asyncio.create_task(instance.start())
                        else:
                            instance.start()
                    except Exception as start_error:
                        self.logger.warning(f"Start method failed for {component_name}: {start_error}")
                        # Continue anyway - component may still be functional
                
                # Store active instance
                self.active_instances[component_name] = instance
                component_status.instance = instance
                component_status.is_active = True
                component_status.last_activation = datetime.now()
                component_status.activation_count += 1
                
                self.logger.info(f"AI component {component_name} successfully activated")
                return True
            else:
                self.logger.error(f"Could not find main class for {component_name}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to activate AI component {component_name}: {e}")
            if component_name in self.ai_components:
                self.ai_components[component_name].error_count += 1
                self.ai_components[component_name].last_error = str(e)
            return False
    
    def _create_instance_with_dependencies(self, main_class, component_name: str):
        """Create instance with proper dependency injection"""
        try:
            # Get class signature to understand required parameters
            sig = inspect.signature(main_class.__init__)
            params = list(sig.parameters.keys())[1:]  # Skip 'self'
            
            kwargs = {}
            
            # Map common dependency parameters
            if 'config' in params and self.config:
                kwargs['config'] = self.config
            if 'bot_config' in params and self.config:
                kwargs['bot_config'] = self.config
            if 'database_manager' in params and self.database_manager:
                kwargs['database_manager'] = self.database_manager
            if 'bybit_client' in params and self.bybit_client:
                kwargs['bybit_client'] = self.bybit_client
            if 'exchange_client' in params and self.bybit_client:
                kwargs['exchange_client'] = self.bybit_client
            
            # Special cases for specific components
            if component_name == 'advanced_memory_system':
                if 'base_memory_manager' in params:
                    # Try to get memory manager from active instances
                    memory_manager = self.active_instances.get('memory_manager')
                    if memory_manager:
                        kwargs['base_memory_manager'] = memory_manager
                    else:
                        # Create a simple memory manager
                        kwargs['base_memory_manager'] = SimpleMemoryManager()
            
            # Try to create instance
            if kwargs:
                instance = main_class(**kwargs)
            else:
                # Try with no arguments for components that don't need dependencies
                try:
                    instance = main_class()
                except TypeError:
                    # If it needs dependencies but we don't have them, create a mock config
                    if 'config' in params:
                        mock_config = type('MockConfig', (), {
                            'ai': {'enabled': True},
                            'database': {'path': 'bybit_trading_bot.db'},
                            'api_keys': {'openrouter': {'api_key': 'mock'}},
                            'trading': {'margin_trading_enabled': True}
                        })()
                        kwargs['config'] = mock_config
                        instance = main_class(**kwargs)
                    else:
                        raise
            
            return instance
            
        except Exception as e:
            self.logger.error(f"Failed to create instance for {component_name}: {e}")
            return None
    
    def _find_main_class(self, module, component_name: str):
        """Find the main class in a module"""
        # Common class naming patterns
        class_patterns = [
            component_name.replace('_', '').title(),
            ''.join(word.capitalize() for word in component_name.split('_')),
            component_name.upper(),
            f"{component_name.replace('_', '').title()}Manager",
            f"{component_name.replace('_', '').title()}Engine",
            f"{component_name.replace('_', '').title()}System",
        ]
        
        # Special case mappings for known components
        special_mappings = {
            'intelligent_ml_system': 'IntelligentMLSystem',
            'advanced_memory_system': 'AdvancedMemorySystem',
            'meta_cognition_engine': 'MetaCognitionEngine',
            'supergpt_integration': 'SuperGPTIntegration',
            'self_correcting_code_evolution': 'SelfCorrectingCodeEvolution',
            'openrouter_client': 'OpenRouterClient',
            'advanced_market_predictor': 'AdvancedMarketPredictor',
            'advanced_risk_manager': 'AdvancedRiskManager',
            'memory_manager': 'PersistentMemoryManager'
        }
        
        # Check special mappings first
        if component_name in special_mappings:
            expected_class = special_mappings[component_name]
            if hasattr(module, expected_class):
                attr = getattr(module, expected_class)
                if inspect.isclass(attr):
                    return attr
        
        for attr_name in dir(module):
            attr = getattr(module, attr_name)
            if inspect.isclass(attr) and not attr_name.startswith('_'):
                # Skip typing imports like Any, Dict, etc.
                if hasattr(attr, '__module__') and attr.__module__ == 'typing':
                    continue
                    
                # Check if class name matches patterns
                if attr_name in class_patterns:
                    return attr
                # Check if it's the main class (usually the largest or has specific methods)
                if hasattr(attr, 'initialize') or hasattr(attr, 'start') or hasattr(attr, 'run'):
                    return attr
        
        # Return first non-private, non-typing class if no match found
        for attr_name in dir(module):
            attr = getattr(module, attr_name)
            if inspect.isclass(attr) and not attr_name.startswith('_'):
                # Skip typing imports
                if hasattr(attr, '__module__') and attr.__module__ == 'typing':
                    continue
                return attr
        
        return None
    
    async def _attempt_failed_system_recovery(self):
        """Attempt to recover failed AI systems"""
        self.logger.info("Attempting recovery of failed AI systems...")
        
        failed_systems = [
            name for name, status in self.ai_components.items()
            if not status.is_active
        ]
        
        for system_name in failed_systems:
            self.logger.info(f"Attempting recovery of {system_name}")
            module_path = self.mandatory_ai_systems.get(system_name)
            if module_path:
                await asyncio.sleep(2)  # Brief delay before retry
                success = await self._activate_ai_component(system_name, module_path)
                if success:
                    self.logger.info(f"Successfully recovered {system_name}")
                else:
                    self.logger.error(f"Failed to recover {system_name}")
    
    async def start_continuous_monitoring(self):
        """Start continuous monitoring and auto-reactivation"""
        self.monitoring_active = True
        self.logger.info("Starting continuous AI system monitoring - AUTO-REACTIVATION MODE")
        
        while self.monitoring_active:
            try:
                await self._check_system_health()
                await asyncio.sleep(30)  # Check every 30 seconds
            except Exception as e:
                self.logger.error(f"Error in AI monitoring: {e}")
                await asyncio.sleep(60)  # Longer delay on error
    
    async def _check_system_health(self):
        """Check health of all AI systems and reactivate if needed"""
        for component_name, status in self.ai_components.items():
            if not status.is_active:
                self.logger.warning(f"AI system {component_name} is inactive - attempting reactivation")
                module_path = self.mandatory_ai_systems.get(component_name)
                if module_path:
                    await self._activate_ai_component(component_name, module_path)
            
            # Check if instance is still responsive
            if status.instance and hasattr(status.instance, 'health_check'):
                try:
                    if inspect.iscoroutinefunction(status.instance.health_check):
                        health = await status.instance.health_check()
                    else:
                        health = status.instance.health_check()
                    
                    if not health:
                        self.logger.warning(f"AI system {component_name} failed health check - reactivating")
                        await self._activate_ai_component(component_name, self.mandatory_ai_systems[component_name])
                except Exception as e:
                    self.logger.error(f"Health check failed for {component_name}: {e}")
    
    def get_ai_system_status(self) -> Dict[str, Any]:
        """Get comprehensive status of all AI systems"""
        return {
            'total_systems': len(self.mandatory_ai_systems),
            'active_systems': sum(1 for status in self.ai_components.values() if status.is_active),
            'inactive_systems': sum(1 for status in self.ai_components.values() if not status.is_active),
            'system_details': {
                name: {
                    'active': status.is_active,
                    'activation_count': status.activation_count,
                    'error_count': status.error_count,
                    'last_activation': status.last_activation.isoformat() if status.last_activation else None,
                    'last_error': status.last_error,
                    'performance_score': status.performance_score
                }
                for name, status in self.ai_components.items()
            },
            'monitoring_active': self.monitoring_active
        }
    
    def get_active_ai_instances(self) -> Dict[str, Any]:
        """Get all active AI instances for integration"""
        return self.active_instances.copy()
    
    async def shutdown_ai_systems(self):
        """Shutdown all AI systems (emergency only)"""
        self.logger.warning("EMERGENCY: Shutting down all AI systems")
        self.monitoring_active = False
        
        for component_name, instance in self.active_instances.items():
            try:
                if hasattr(instance, 'shutdown') and callable(getattr(instance, 'shutdown')):
                    if inspect.iscoroutinefunction(instance.shutdown):
                        await instance.shutdown()
                    else:
                        instance.shutdown()
                self.logger.info(f"AI system {component_name} shutdown complete")
            except Exception as e:
                self.logger.error(f"Error shutting down {component_name}: {e}")
        
        self.active_instances.clear()
        for status in self.ai_components.values():
            status.is_active = False
    
    async def force_reactivate_all(self):
        """Force reactivation of all AI systems"""
        self.logger.info("FORCE REACTIVATION: Restarting all AI systems")
        await self.shutdown_ai_systems()
        await asyncio.sleep(5)  # Brief pause
        await self.activate_all_ai_systems()


# Global AI Activation Manager instance - will be reinitialized with dependencies
ai_activation_manager = None


def initialize_ai_activation_manager(config=None, database_manager=None, bybit_client=None):
    """Initialize the global AI activation manager with dependencies"""
    global ai_activation_manager
    ai_activation_manager = AIFolderActivationManager(config, database_manager, bybit_client)
    return ai_activation_manager


async def activate_ai_folder(config=None, database_manager=None, bybit_client=None):
    """Main function to activate the entire AI folder"""
    global ai_activation_manager
    if ai_activation_manager is None or config is not None:
        ai_activation_manager = AIFolderActivationManager(config, database_manager, bybit_client)
    return await ai_activation_manager.activate_all_ai_systems()


async def start_ai_monitoring():
    """Start continuous AI monitoring"""
    return await ai_activation_manager.start_continuous_monitoring()


def get_ai_instances():
    """Get all active AI instances"""
    return ai_activation_manager.get_active_ai_instances()


def get_ai_status():
    """Get AI system status"""
    return ai_activation_manager.get_ai_system_status()
