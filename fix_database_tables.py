#!/usr/bin/env python3
"""
CRITICAL DATABASE FIX - CREATE MISSING TABLES
"""
import sqlite3
import os
import sys

def fix_database_tables():
    """Create missing database tables"""
    db_path = "bybit_trading_bot.db"
    
    if not os.path.exists(db_path):
        print(f"ERROR: Database file {db_path} not found!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("Creating missing system_state table...")
        
        # Create system_state table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS system_state (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                component VARCHAR(50) UNIQUE NOT NULL,
                last_run_timestamp DATETIME NOT NULL,
                state_data TEXT DEFAULT '{}',
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create other critical missing tables
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trading_memories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol VARCHAR(20) NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                pattern_data TEXT NOT NULL,
                pattern_hash VARCHAR(64) NOT NULL,
                outcome TEXT NOT NULL,
                success BOOLEAN NOT NULL,
                profit_loss REAL DEFAULT 0,
                strategy VARCHAR(50),
                confidence REAL DEFAULT 0,
                market_conditions TEXT,
                technical_indicators TEXT,
                metadata TEXT DEFAULT '{}',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS strategy_memories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                strategy_name VARCHAR(50) NOT NULL,
                memory_data TEXT NOT NULL,
                performance_score REAL DEFAULT 0,
                total_trades INTEGER DEFAULT 0,
                winning_trades INTEGER DEFAULT 0,
                total_pnl REAL DEFAULT 0,
                max_drawdown REAL DEFAULT 0,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trading_memories_symbol ON trading_memories(symbol)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_strategy_memories_name ON strategy_memories(strategy_name)")
        
        conn.commit()
        conn.close()
        
        print("SUCCESS: All missing database tables created!")
        return True
        
    except Exception as e:
        print(f"ERROR creating database tables: {e}")
        return False

if __name__ == "__main__":
    # Change directory first
    try:
        os.chdir(r"e:\The_real_deal_copy\Bybit_Bot\BOT")
        print(f"Changed directory to: {os.getcwd()}")
    except Exception as e:
        print(f"ERROR changing directory: {e}")
        sys.exit(1)
    
    success = fix_database_tables()
    if success:
        print("Database fix completed successfully!")
    else:
        print("Database fix failed!")
        sys.exit(1)
