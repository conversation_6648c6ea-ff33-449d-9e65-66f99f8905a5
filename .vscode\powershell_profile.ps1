# PowerShell Profile – Bybit Trading Bot Environment + Copilot Shell Integration

##########################
# Shell Integration Beginning – must load before other init
##########################
if ($env:TERM_PROGRAM -eq 'vscode') {
    try {
        . "$(code --locate-shell-integration-path pwsh)"
    } catch {
        Write-Host "Warning: Failed to load VS Code shell integration" -ForegroundColor Yellow
    }
}

##########################
# Workspace & Conda Setup
##########################
Write-Host "Initializing Bybit Trading Bot PowerShell Environment..." -ForegroundColor Cyan

if (-not (Get-Command conda -ErrorAction SilentlyContinue)) {
    Write-Host "Initializing conda…" -ForegroundColor Yellow
    & "E:\conda\miniconda3\shell\condabin\conda-hook.ps1"
}

Write-Host "Activating conda environment: bybit-trader" -ForegroundColor Yellow
conda activate bybit-trader

Set-Location "E:\The_real_deal_copy\Bybit_Bot\BOT"

$env:PYTHONPATH = "E:\The_real_deal_copy\Bybit_Bot\BOT"
$env:CONDA_DEFAULT_ENV = "bybit-trader"
$env:CONDA_PREFIX = "E:\conda\envs\bybit-trader"

Write-Host "[OK] Environment Setup Complete:" -ForegroundColor Green
Write-Host "  - Conda Environment: $env:CONDA_DEFAULT_ENV" -ForegroundColor White
Write-Host "  - Python Path: $env:PYTHONPATH" -ForegroundColor White
Write-Host "  - Working Directory: $(Get-Location)" -ForegroundColor White
Write-Host "  - Python Version: " -NoNewline -ForegroundColor White
python --version

Write-Host "`nAvailable Commands:" -ForegroundColor Cyan
Write-Host "  python main.py          - Run the trading bot" -ForegroundColor White
Write-Host "  python debug_*.py       - Run debug scripts" -ForegroundColor White
Write-Host "  conda list              - Show installed packages" -ForegroundColor White
Write-Host "  conda info              - Show conda info" -ForegroundColor White

##########################
# Prompt Decoration for Shell Integration Marks
##########################
$Global:__LastHistoryId = -1

function Global:__Terminal-Get-LastExitCode {
    if ($? -eq $True) { return 0 }
    $LastHistoryEntry = Get-History -Count 1
    $IsPowerShellError = $Error[0].InvocationInfo.HistoryId -eq $LastHistoryEntry.Id
    if ($IsPowerShellError) { return -1 }
    return $LastExitCode
}

function prompt {
    $gle = __Terminal-Get-LastExitCode
    $LastHistoryEntry = Get-History -Count 1

    if ($Global:__LastHistoryId -ne -1) {
        if ($LastHistoryEntry.Id -eq $Global:__LastHistoryId) {
            $out = "`e]133;D`a"
        } else {
            $out = "`e]133;D;$gle`a"
        }
    }

    $Global:__LastHistoryId = $LastHistoryEntry.Id
    Write-Host $out -NoNewline

    return "PS $(Get-Location)> "
}
