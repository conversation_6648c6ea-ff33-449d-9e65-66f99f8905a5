@echo off
echo [INFO] MCP Diagnostic Script
echo [INFO] Testing MCP server connections...
echo.

echo [INFO] Testing npx-y alias...
npx-y --version
if %ERRORLEVEL% neq 0 (
    echo [ERROR] npx-y alias failed
    exit /b 1
)
echo [OK] npx-y alias working

echo.
echo [INFO] Testing Sequential Thinking MCP Server...
npx-y @modelcontextprotocol/server-sequential-thinking@latest --help
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Sequential Thinking MCP server failed
    exit /b 1
)
echo [OK] Sequential Thinking MCP server working

echo.
echo [INFO] Testing Context7 MCP Server...
npx-y @upstash/context7-mcp@latest --help
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Context7 MCP server failed
    exit /b 1
)
echo [OK] Context7 MCP server working

echo.
echo [INFO] Testing Memory MCP Server...
npx-y @modelcontextprotocol/server-memory@latest --help
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Memory MCP server failed
    exit /b 1
)
echo [OK] Memory MCP server working

echo.
echo [INFO] All MCP servers are responding correctly
echo [INFO] VS Code crash may be due to other factors
echo.
echo [INFO] Troubleshooting suggestions:
echo 1. Try with minimal MCP config (only one server)
echo 2. Check VS Code Insiders version compatibility
echo 3. Clear VS Code cache and restart
echo 4. Check for conflicting extensions
echo.
pause
