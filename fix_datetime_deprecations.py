#!/usr/bin/env python3
"""
Fix all datetime.utcnow() deprecation issues
AUTOMATED_MANUAL.md Section 0.2 - Zero tolerance for Pylance errors
"""

import os
import re
from pathlib import Path

def fix_datetime_deprecations():
    """Fix all datetime.utcnow() occurrences"""
    print("FIXING DATETIME DEPRECATIONS...")
    
    bybit_bot_path = Path("bybit_bot")
    files_fixed = 0
    total_replacements = 0
    
    # Find all Python files
    for py_file in bybit_bot_path.rglob("*.py"):
        try:
            content = py_file.read_text(encoding='utf-8')
            original_content = content
            
            # Check if file contains datetime.utcnow()
            if "datetime.utcnow()" in content:
                print(f"  FIXING: {py_file.relative_to(bybit_bot_path)}")
                
                # Ensure timezone import exists
                if "from datetime import" in content and "timezone" not in content:
                    # Add timezone to existing datetime import
                    content = re.sub(
                        r'from datetime import ([^,\n]+)(?:,\s*([^,\n]+))*',
                        lambda m: f"from datetime import {m.group(1)}, timezone" + 
                                 (f", {m.group(2)}" if m.group(2) else ""),
                        content
                    )
                elif "import datetime" in content and "from datetime import" not in content:
                    # Add timezone import line
                    content = re.sub(
                        r'(import datetime\n)',
                        r'\1from datetime import timezone\n',
                        content
                    )
                
                # Replace all datetime.utcnow() with datetime.now(timezone.utc)
                replacements = len(re.findall(r'datetime\.utcnow\(\)', content))
                content = re.sub(
                    r'datetime\.utcnow\(\)',
                    'datetime.now(timezone.utc)',
                    content
                )
                
                # Write back if changes were made
                if content != original_content:
                    py_file.write_text(content, encoding='utf-8')
                    files_fixed += 1
                    total_replacements += replacements
                    print(f"    FIXED: {replacements} occurrences")
                    
        except Exception as e:
            print(f"  ERROR: Could not process {py_file}: {e}")
            continue
    
    print(f"DATETIME DEPRECATION FIXES COMPLETED")
    print(f"Files fixed: {files_fixed}")
    print(f"Total replacements: {total_replacements}")
    return files_fixed, total_replacements

if __name__ == "__main__":
    fix_datetime_deprecations()
