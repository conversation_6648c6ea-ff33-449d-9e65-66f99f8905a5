"""
Test Component Initialization
Tests if the AI system components can initialize without database errors
"""
import asyncio
import logging
import sys
import os
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s | %(message)s')
logger = logging.getLogger(__name__)

async def test_component_initialization():
    """Test if components can initialize without database errors"""
    try:
        logger.info("Testing component initialization...")
        
        # Add current directory to path
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # Import required modules
        from bybit_bot.core.config import EnhancedBotConfig
        from bybit_bot.database.connection import DatabaseManager

        # Create configuration
        logger.info("Creating configuration...")
        config = EnhancedBotConfig()
        logger.info("Configuration created successfully")
        
        # Create database manager
        logger.info("Creating database manager...")
        db_manager = DatabaseManager(config)
        await db_manager.initialize()
        logger.info("Database manager initialized successfully")
        
        # Test MetaCognitionEngine initialization
        logger.info("Testing MetaCognitionEngine initialization...")
        try:
            from bybit_bot.ai.meta_cognition_engine import MetaCognitionEngine
            meta_cognition = MetaCognitionEngine(
                config=config,
                database_manager=db_manager
            )
            logger.info("SUCCESS: MetaCognitionEngine created without errors")
            
            # Test initialization
            await meta_cognition.initialize()
            logger.info("SUCCESS: MetaCognitionEngine initialized without errors")
            
        except Exception as e:
            logger.error(f"ERROR: MetaCognitionEngine initialization failed: {e}")
            return False
        
        # Test AutonomyEngine initialization
        logger.info("Testing AutonomyEngine initialization...")
        try:
            from bybit_bot.core.autonomy_engine import AutonomyEngine
            
            # Create required dependencies
            from bybit_bot.core.time_manager import EnhancedTimeManager
            from bybit_bot.ai.memory_manager import PersistentMemoryManager
            from bybit_bot.ai.advanced_risk_manager import AdvancedRiskManager
            from bybit_bot.core.agent_orchestrator import AgentOrchestrator
            
            time_manager = EnhancedTimeManager(config)
            memory_manager = PersistentMemoryManager(config, db_manager)
            risk_manager = AdvancedRiskManager(config, db_manager)
            agent_orchestrator = AgentOrchestrator(config, db_manager)
            
            autonomy_engine = AutonomyEngine(
                config=config,
                database_manager=db_manager,
                time_manager=time_manager,
                memory_manager=memory_manager,
                risk_manager=risk_manager,
                agent_orchestrator=agent_orchestrator
            )
            logger.info("SUCCESS: AutonomyEngine created without errors")
            
        except Exception as e:
            logger.error(f"ERROR: AutonomyEngine initialization failed: {e}")
            # This is expected since we don't have all dependencies
            logger.info("NOTE: AutonomyEngine failure is expected due to missing dependencies")
        
        # Test CodeOptimizer initialization
        logger.info("Testing CodeOptimizer initialization...")
        try:
            from bybit_bot.core.code_optimizer import CodeOptimizer
            code_optimizer = CodeOptimizer(
                config=config,
                database_manager=db_manager
            )
            logger.info("SUCCESS: CodeOptimizer created without errors")
            
        except Exception as e:
            logger.error(f"ERROR: CodeOptimizer initialization failed: {e}")
            return False
        
        # Close database connection
        await db_manager.close()
        logger.info("Database connection closed")
        
        logger.info("SUCCESS: Component initialization test completed!")
        return True
        
    except Exception as e:
        logger.error(f"FAILED: Component initialization test failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_component_initialization())
    if success:
        print("Component initialization test passed!")
    else:
        print("Component initialization test failed!")
