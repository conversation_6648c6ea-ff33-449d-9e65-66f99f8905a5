#!/usr/bin/env python3
"""
CRITICAL FIXES - BULK SYSTEM REPAIR
"""
import os
import sys
import sqlite3
import time
from pathlib import Path

def fix_all_critical_issues():
    """Fix all critical system issues in one go"""
    
    print("CRITICAL FIXES - BULK SYSTEM REPAIR")
    print("=" * 50)
    
    # Change to correct directory
    try:
        os.chdir(r"e:\The_real_deal_copy\Bybit_Bot\BOT")
        print(f"Working directory: {os.getcwd()}")
    except Exception as e:
        print(f"ERROR changing directory: {e}")
        return False
    
    success_count = 0
    total_fixes = 0
    
    # Fix 1: Create missing database tables
    print("\n[FIX 1] Creating missing database tables...")
    total_fixes += 1
    if fix_database_tables():
        success_count += 1
        print("SUCCESS: Database tables created")
    else:
        print("FAILED: Database table creation")
    
    # Fix 2: Verify enhanced client methods exist
    print("\n[FIX 2] Verifying enhanced client methods...")
    total_fixes += 1
    if verify_enhanced_client():
        success_count += 1
        print("SUCCESS: Enhanced client methods verified")
    else:
        print("FAILED: Enhanced client methods missing")
    
    # Fix 3: Check API configuration
    print("\n[FIX 3] Checking API configuration...")
    total_fixes += 1
    if check_api_config():
        success_count += 1
        print("SUCCESS: API configuration checked")
    else:
        print("FAILED: API configuration issues")
    
    print(f"\nFIXES COMPLETED: {success_count}/{total_fixes}")
    
    if success_count == total_fixes:
        print("ALL FIXES SUCCESSFUL!")
        return True
    else:
        print(f"PARTIAL SUCCESS: {total_fixes - success_count} fixes failed")
        return False

def fix_database_tables():
    """Create missing database tables"""
    try:
        db_path = "bybit_trading_bot.db"
        
        if not os.path.exists(db_path):
            print(f"WARNING: Database file {db_path} not found - creating new one")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create system_state table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS system_state (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                component VARCHAR(50) UNIQUE NOT NULL,
                last_run_timestamp DATETIME NOT NULL,
                state_data TEXT DEFAULT '{}',
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create trading_memories table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trading_memories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol VARCHAR(20) NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                pattern_data TEXT NOT NULL,
                pattern_hash VARCHAR(64) NOT NULL,
                outcome TEXT NOT NULL,
                success BOOLEAN NOT NULL,
                profit_loss REAL DEFAULT 0,
                strategy VARCHAR(50),
                confidence REAL DEFAULT 0,
                market_conditions TEXT,
                technical_indicators TEXT,
                metadata TEXT DEFAULT '{}',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create strategy_memories table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS strategy_memories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                strategy_name VARCHAR(50) NOT NULL,
                memory_data TEXT NOT NULL,
                performance_score REAL DEFAULT 0,
                total_trades INTEGER DEFAULT 0,
                winning_trades INTEGER DEFAULT 0,
                total_pnl REAL DEFAULT 0,
                max_drawdown REAL DEFAULT 0,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trading_memories_symbol ON trading_memories(symbol)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_strategy_memories_name ON strategy_memories(strategy_name)")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"Database fix error: {e}")
        return False

def verify_enhanced_client():
    """Verify enhanced client has required methods"""
    try:
        client_path = Path("bybit_bot/exchange/enhanced_bybit_client.py")
        
        if not client_path.exists():
            print(f"Enhanced client file not found: {client_path}")
            return False
        
        with open(client_path, 'r') as f:
            content = f.read()
        
        # Check for required methods
        required_methods = ["get_account_balance", "get_market_data"]
        missing_methods = []
        
        for method in required_methods:
            if f"def {method}" not in content and f"async def {method}" not in content:
                missing_methods.append(method)
        
        if missing_methods:
            print(f"Missing methods in enhanced client: {missing_methods}")
            return False
        
        return True
        
    except Exception as e:
        print(f"Enhanced client verification error: {e}")
        return False

def check_api_config():
    """Check API configuration"""
    try:
        config_path = Path("config.yaml")
        
        if not config_path.exists():
            print("Config file not found: config.yaml")
            return False
        
        # Simple YAML parsing without external dependency
        with open(config_path, 'r') as f:
            content = f.read()
        
        # Basic check for bybit configuration
        if 'bybit:' not in content and 'bybit ' not in content:
            print("No bybit configuration found in config.yaml")
            return False
        
        # Look for api_key and api_secret lines
        lines = content.split('\n')
        api_key = None
        api_secret = None
        
        for line in lines:
            if 'api_key:' in line:
                api_key = line.split('api_key:')[-1].strip().strip('"\'')
            elif 'api_secret:' in line:
                api_secret = line.split('api_secret:')[-1].strip().strip('"\'')
        
        if not api_key or not api_secret:
            print("Missing API credentials in config")
            return False
        
        if len(api_key) < 10 or len(api_secret) < 20:
            print("API credentials appear invalid (too short)")
            return False
        
        print(f"API config found - Key: {api_key[:8]}..., Secret: {api_secret[:8]}...")
        return True
        
    except Exception as e:
        print(f"API config check error: {e}")
        return False

if __name__ == "__main__":
    success = fix_all_critical_issues()
    if success:
        print("\nALL CRITICAL FIXES COMPLETED SUCCESSFULLY!")
        sys.exit(0)
    else:
        print("\nSOME FIXES FAILED - MANUAL INTERVENTION REQUIRED")
        sys.exit(1)
