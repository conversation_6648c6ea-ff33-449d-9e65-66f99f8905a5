#!/usr/bin/env python3
"""
Test Bybit API connection with actual credentials
"""

from dotenv import load_dotenv
import os
import sys

# Load environment variables
load_dotenv()

def test_bybit_connection():
    """Test connection to Bybit API"""
    try:
        # Get API credentials
        api_key = os.getenv('BYBIT_API_KEY')
        api_secret = os.getenv('BYBIT_API_SECRET')
        
        print(f"API Key: {api_key[:10]}...")
        print(f"API Secret: {api_secret[:10]}...")
        
        # Test with pybit
        from pybit.unified_trading import HTTP
        
        session = HTTP(
            testnet=False,
            api_key=api_key,
            api_secret=api_secret
        )
        
        print("Testing Bybit connection...")
        
        # Test wallet balance
        balance_response = session.get_wallet_balance(accountType="UNIFIED")
        print("SUCCESS: Connected to Bybit")
        print(f"Balance response: {balance_response}")
        
        # Test account info
        account_info = session.get_account_info()
        print(f"Account info: {account_info}")
        
        return True
        
    except Exception as e:
        print(f"ERROR connecting to Bybit: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_bybit_connection()
    if success:
        print("BYBIT CONNECTION TEST: PASSED")
    else:
        print("BYBIT CONNECTION TEST: FAILED")
        sys.exit(1)
