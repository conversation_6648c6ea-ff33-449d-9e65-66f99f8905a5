#!/usr/bin/env python3
"""
Test Python Environment and Start Trading Bot
"""

print("TESTING PYTHON ENVIRONMENT...")
print("Python version check...")

import sys
print(f"Python version: {sys.version}")

print("Testing imports...")
try:
    import asyncio
    print("✓ asyncio imported")
except ImportError as e:
    print(f"✗ asyncio failed: {e}")

try:
    from dotenv import load_dotenv
    print("✓ dotenv imported")
except ImportError as e:
    print(f"✗ dotenv failed: {e}")

try:
    import sqlite3
    print("✓ sqlite3 imported")
except ImportError as e:
    print(f"✗ sqlite3 failed: {e}")

print("Environment test completed!")

# If all imports work, try to start the trading bot
print("\nSTARTING TRADING BOT...")
try:
    import main
    print("✓ main.py imported successfully")
except Exception as e:
    print(f"✗ main.py import failed: {e}")
    import traceback
    traceback.print_exc()
