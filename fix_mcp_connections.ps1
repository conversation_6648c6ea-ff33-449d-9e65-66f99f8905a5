# Fix MCP Connection Issues for VS Code - PowerShell Version
Write-Host "[INFO] Fixing VS Code MCP connection issues..." -ForegroundColor Green

# Create npx-y alias to fix VS Code MCP configuration
$npxPath = Get-Command npx -ErrorAction SilentlyContinue
if ($npxPath) {
    $npxDir = Split-Path $npxPath.Source
    $npxYPath = Join-Path $npxDir "npx-y.cmd"
    
    if (-not (Test-Path $npxYPath)) {
        Write-Host "[INFO] Creating npx-y alias for VS Code compatibility..." -ForegroundColor Yellow
        "@echo off`nnpx %*" | Out-File -FilePath $npxYPath -Encoding ASCII
        Write-Host "[OK] Created npx-y.cmd at: $npxYPath" -ForegroundColor Green
    } else {
        Write-Host "[OK] npx-y.cmd already exists" -ForegroundColor Green
    }
} else {
    Write-Host "[ERROR] npx not found in PATH" -ForegroundColor Red
    exit 1
}

# Test MCP server connections
Write-Host "`n[INFO] Testing MCP server connections..." -ForegroundColor Cyan

# Test Context7 MCP
Write-Host "Testing Context7 MCP server..." -ForegroundColor Yellow
try {
    $result = & npx @upstash/context7-mcp@latest --help 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "[OK] Context7 MCP server accessible" -ForegroundColor Green
    } else {
        Write-Host "[WARNING] Context7 MCP server test failed" -ForegroundColor Yellow
    }
} catch {
    Write-Host "[WARNING] Context7 MCP server not accessible: $_" -ForegroundColor Yellow
}

# Test Sequential Thinking MCP
Write-Host "Testing Sequential Thinking MCP server..." -ForegroundColor Yellow
try {
    $process = Start-Process -FilePath "npx" -ArgumentList "@modelcontextprotocol/server-sequential-thinking@latest", "--help" -PassThru -WindowStyle Hidden
    Start-Sleep -Seconds 2
    if (-not $process.HasExited) {
        $process.Kill()
        Write-Host "[OK] Sequential Thinking MCP server accessible" -ForegroundColor Green
    } else {
        Write-Host "[WARNING] Sequential Thinking MCP server test failed" -ForegroundColor Yellow
    }
} catch {
    Write-Host "[WARNING] Sequential Thinking MCP server not accessible: $_" -ForegroundColor Yellow
}

# Test uvx command
Write-Host "Testing uvx command..." -ForegroundColor Yellow
try {
    $result = & uvx --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "[OK] uvx command accessible" -ForegroundColor Green
    } else {
        Write-Host "[WARNING] uvx command test failed" -ForegroundColor Yellow
    }
} catch {
    Write-Host "[WARNING] uvx command not accessible: $_" -ForegroundColor Yellow
}

Write-Host "`n[INFO] MCP fix script completed!" -ForegroundColor Green
Write-Host "[INFO] Please restart VS Code for changes to take effect." -ForegroundColor Cyan
Write-Host "[INFO] The following MCP servers should now work:" -ForegroundColor Cyan
Write-Host "  - Context 7: npx @upstash/context7-mcp@latest" -ForegroundColor White
Write-Host "  - Sequential thinking: npx @modelcontextprotocol/server-sequential-thinking@latest" -ForegroundColor White
Write-Host "  - Redis: uvx --from git+https://github.com/redis/mcp-redis.git redis-mcp-server" -ForegroundColor White

Read-Host "Press Enter to continue..."
