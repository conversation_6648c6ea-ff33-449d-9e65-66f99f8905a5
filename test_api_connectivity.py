"""
Test API Connectivity and Authentication
Verifies that the Bybit API keys can successfully authenticate and connect
"""
import asyncio
import logging
import sys
import os
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s | %(message)s')
logger = logging.getLogger(__name__)

async def test_api_connectivity():
    """Test Bybit API connectivity and authentication"""
    try:
        logger.info("Testing Bybit API connectivity and authentication...")
        
        # Add current directory to path
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # Import required modules
        from bybit_bot.core.config import EnhancedBotConfig
        from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
        
        # Create configuration
        logger.info("Creating configuration...")
        config = EnhancedBotConfig()
        logger.info("Configuration created successfully")
        
        # Verify API keys are loaded
        api_key = config.api_keys.bybit.get('api_key')
        api_secret = config.api_keys.bybit.get('api_secret')
        
        if not api_key or not api_secret:
            logger.error("ERROR: API keys not found in configuration")
            return False
        
        logger.info(f"API Key loaded: {api_key[:10]}...")
        logger.info(f"API Secret loaded: {api_secret[:10]}...")
        
        # Create Bybit client
        logger.info("Creating Bybit client...")
        bybit_client = EnhancedBybitClient(config)

        # Initialize the client
        logger.info("Initializing Bybit client...")
        await bybit_client.initialize()
        logger.info("Bybit client initialized successfully")
        
        # Test basic API connectivity
        logger.info("Testing basic API connectivity...")
        try:
            # Test server time (no authentication required)
            server_time = await bybit_client._get_server_time()
            logger.info(f"SUCCESS: Server time retrieved: {server_time}")
        except Exception as e:
            logger.error(f"ERROR: Failed to get server time: {e}")
            return False
        
        # Test authentication
        logger.info("Testing API authentication...")
        try:
            # Get account info (requires authentication)
            account_info = await bybit_client.get_account_info()
            if account_info:
                logger.info("SUCCESS: Account info retrieved successfully")
                logger.info(f"Account type: {account_info.get('accountType', 'Unknown')}")
                logger.info(f"Account status: {account_info.get('status', 'Unknown')}")
                
                # Check wallet balance
                wallet_balance = await bybit_client.get_wallet_balance()
                if wallet_balance:
                    logger.info("SUCCESS: Wallet balance retrieved successfully")
                    for coin_info in wallet_balance.get('list', []):
                        for coin in coin_info.get('coin', []):
                            if float(coin.get('walletBalance', 0)) > 0:
                                logger.info(f"Balance: {coin.get('coin')} = {coin.get('walletBalance')}")
                else:
                    logger.warning("WARNING: Could not retrieve wallet balance")
            else:
                logger.error("ERROR: Account info is empty")
                return False
        except Exception as e:
            logger.error(f"ERROR: Authentication failed: {e}")
            return False
        
        # Test market data access
        logger.info("Testing market data access...")
        try:
            # Get ticker info for BTCUSDT
            ticker_info = await bybit_client.get_ticker('BTCUSDT')
            if ticker_info:
                logger.info("SUCCESS: Market data retrieved successfully")
                logger.info(f"BTCUSDT Price: {ticker_info.get('lastPrice', 'Unknown')}")
            else:
                logger.warning("WARNING: Could not retrieve market data")
        except Exception as e:
            logger.error(f"ERROR: Market data access failed: {e}")
            return False
        
        # Test order placement capability (dry run)
        logger.info("Testing order placement capability (dry run)...")
        try:
            # Get position info (requires authentication)
            positions = await bybit_client.get_positions()
            logger.info("SUCCESS: Position info retrieved successfully")
            logger.info(f"Number of positions: {len(positions) if positions else 0}")
        except Exception as e:
            logger.error(f"ERROR: Position info access failed: {e}")
            return False
        
        # Test rate limiting
        logger.info("Testing rate limiting...")
        try:
            # Make multiple rapid requests to test rate limiting
            for i in range(3):
                await bybit_client._get_server_time()
                logger.info(f"Rate limit test {i+1}/3 passed")
                await asyncio.sleep(0.1)  # Small delay
        except Exception as e:
            logger.error(f"ERROR: Rate limiting test failed: {e}")
            return False
        
        logger.info("SUCCESS: All API connectivity tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"FAILED: API connectivity test failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_api_connectivity())
    if success:
        print("API connectivity test passed!")
    else:
        print("API connectivity test failed!")
