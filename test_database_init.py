#!/usr/bin/env python3
"""
Test database initialization to identify any remaining SQLite syntax issues
"""

import sys
import traceback
import asyncio
from pathlib import Path

async def test_database_initialization():
    """Test the database initialization process"""
    print("TESTING DATABASE INITIALIZATION...")
    print("=" * 50)
    
    try:
        # Test 1: Import and create config
        print("TEST 1: Loading configuration...")
        from bybit_bot.core.config import EnhancedBotConfig
        config = EnhancedBotConfig()
        print("  SUCCESS: Configuration loaded")
        
        # Test 2: Import and create database manager
        print("\nTEST 2: Creating database manager...")
        from bybit_bot.database.connection import DatabaseManager
        db_manager = DatabaseManager(config)
        print("  SUCCESS: Database manager created")
        
        # Test 3: Initialize database
        print("\nTEST 3: Initializing database...")
        await db_manager.initialize()
        print("  SUCCESS: Database initialized")
        
        # Test 4: Test connection
        print("\nTEST 4: Testing database connection...")
        await db_manager.test_connection()
        print("  SUCCESS: Database connection test passed")
        
        # Test 5: Test basic operations
        print("\nTEST 5: Testing basic database operations...")
        
        # Test saving a trade
        trade_data = {
            "symbol": "BTCUSDT",
            "side": "buy",
            "quantity": 0.001,
            "price": 50000.0,
            "order_id": "test_order_123",
            "exchange_order_id": "bybit_test_456",
            "status": "completed",
            "strategy": "test_strategy",
            "profit_loss": 0.0,
            "fees": 0.1
        }
        
        trade_id = await db_manager.save_trade(trade_data)
        print(f"  SUCCESS: Trade saved with ID: {trade_id}")
        
        # Test retrieving trades
        trades = await db_manager.get_recent_trades(limit=1)
        print(f"  SUCCESS: Retrieved {len(trades)} recent trades")
        
        # Test 6: Close database connections
        print("\nTEST 6: Closing database connections...")
        await db_manager.close()
        print("  SUCCESS: Database connections closed")
        
        print("\n" + "=" * 50)
        print("DATABASE INITIALIZATION TEST COMPLETE - ALL TESTS PASSED")
        return True
        
    except Exception as e:
        print(f"\nERROR in database initialization: {e}")
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    success = await test_database_initialization()
    if success:
        print("\nSUCCESS: Database initialization working correctly")
        return 0
    else:
        print("\nFAILURE: Database initialization failed")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
