"""
Fix AI System Database Tables
Creates the missing AI system tables with SQLite-compatible syntax
"""
import sqlite3
import logging
import os
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s | %(message)s')
logger = logging.getLogger(__name__)

def fix_ai_system_tables():
    """Create AI system tables with SQLite-compatible syntax"""
    try:
        # Database path
        db_path = Path("data/trading_bot.db")
        db_path.parent.mkdir(exist_ok=True)
        
        logger.info(f"Connecting to database: {db_path}")
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Create AI system tables with SQLite-compatible syntax
        ai_tables = [
            """
            CREATE TABLE IF NOT EXISTS meta_cognition_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                awareness_level REAL,
                cognitive_load REAL,
                system_health REAL,
                adaptation_score REAL,
                learning_efficiency REAL,
                metrics_data TEXT
            )
            """,
            """
            CREATE TABLE IF NOT EXISTS code_evolution_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                file_path VARCHAR(500),
                change_type VARCHAR(50),
                improvement_score REAL,
                performance_impact REAL,
                change_description TEXT,
                change_data TEXT
            )
            """,
            """
            CREATE TABLE IF NOT EXISTS recursive_improvement_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                optimization_level INTEGER,
                improvement_iteration INTEGER,
                convergence_score REAL,
                efficiency_gain REAL,
                optimization_data TEXT
            )
            """,
            """
            CREATE TABLE IF NOT EXISTS ai_system_interactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                source_system VARCHAR(100),
                target_system VARCHAR(100),
                interaction_type VARCHAR(50),
                success BOOLEAN,
                response_time_ms INTEGER,
                interaction_data TEXT
            )
            """
        ]
        
        # Execute each table creation individually
        for i, table_sql in enumerate(ai_tables):
            try:
                cursor.execute(table_sql.strip())
                table_name = table_sql.split()[5]  # Extract table name
                logger.info(f"Created table {i+1}/{len(ai_tables)}: {table_name}")
            except Exception as e:
                logger.error(f"Failed to create table {i+1}: {e}")
                continue
        
        # Create indexes for performance
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_meta_cognition_timestamp ON meta_cognition_metrics(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_code_evolution_timestamp ON code_evolution_history(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_recursive_improvement_timestamp ON recursive_improvement_metrics(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_ai_interactions_timestamp ON ai_system_interactions(timestamp)"
        ]
        
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
                logger.info(f"Created index: {index_sql.split()[5]}")
            except Exception as e:
                logger.warning(f"Failed to create index: {e}")
        
        conn.commit()
        conn.close()
        
        logger.info("SUCCESS: All AI system tables created with SQLite-compatible syntax!")
        return True
        
    except Exception as e:
        logger.error(f"FAILED to fix AI system tables: {e}")
        return False

if __name__ == "__main__":
    success = fix_ai_system_tables()
    if success:
        print("AI system tables fixed successfully!")
    else:
        print("Failed to fix AI system tables!")
