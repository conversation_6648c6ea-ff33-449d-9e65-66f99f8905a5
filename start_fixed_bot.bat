@echo off
echo ================================================
echo BYBIT TRADING BOT - SINGLE ENTRY POINT ONLY
echo USING ONLY main.py - ALL OTHER ENTRY POINTS DELETED
echo ================================================
echo.

echo Cleaning up obsolete entry points first...
python emergency_cleanup_entry_points.py
if %errorlevel% neq 0 (
    echo ERROR: Could not clean up obsolete entry points
    pause
    exit /b 1
)

echo.
echo Activating conda environment: bybit-trader
call conda activate bybit-trader
if %errorlevel% neq 0 (
    echo ERROR: Could not activate conda environment
    pause
    exit /b 1
)

echo.
echo Verifying critical fixes...
python verify_fixes_direct.py
if %errorlevel% neq 0 (
    echo ERROR: Critical fixes verification failed
    pause
    exit /b 1
)

echo.
echo ================================================
echo STARTING BYBIT TRADING BOT USING ONLY main.py
echo ================================================
echo.

echo Current directory: %cd%
echo Python version:
python --version
echo.

echo Starting SINGLE ENTRY POINT: main.py
python main.py

echo.
echo Bot execution completed.
pause
