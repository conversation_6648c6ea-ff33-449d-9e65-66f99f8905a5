#!/usr/bin/env python3
"""
DIRECT DELETION OF ALL OBSOLETE ENTRY POINTS
EXECUTES IMMEDIATELY TO CLEAN UP THE SYSTEM
"""

import os
import sys

def delete_file_if_exists(filename):
    """Delete a file if it exists"""
    try:
        if os.path.exists(filename):
            os.remove(filename)
            print(f"✅ DELETED: {filename}")
            return True
        else:
            print(f"⚠️  NOT FOUND: {filename}")
            return False
    except Exception as e:
        print(f"❌ ERROR deleting {filename}: {e}")
        return False

def main():
    """Execute immediate deletion of all obsolete entry points"""
    print("🗑️" * 60)
    print("EXECUTING IMMEDIATE DELETION OF ALL OBSOLETE ENTRY POINTS")
    print("ONLY main.py IS ALLOWED!")
    print("🗑️" * 60)
    
    # List of ALL obsolete entry points - MUST BE DELETED NOW
    obsolete_files = [
        # Main duplicates - DELETE NOW
        "launch_unified_system.py",
        "system_launcher.py",
        
        # Startup variants - DELETE ALL
        "startup_full_system.py",
        "start_enhanced_ai_system.py", 
        "system_startup_optimized.py",
        "system_startup_simple.py",
        
        # Run variants - DELETE ALL
        "run_system.py",
        "run_live_trading.py",
        "run_main_with_output.py", 
        "run_system_with_six.py",
        "install_six_and_run.py",
        
        # Fix/restart - DELETE
        "fix_and_restart_system.py",
        
        # Test startup - DELETE
        "test_startup.py",
        "test_startup_no_emergency.py",
        
        # Validation/force - DELETE
        "validate_main_system.py",
        "system_verification.py",
        "validate_live_trading.py", 
        "force_live_trading.py",
        "force_trade_execution.py",
        
        # Batch files - DELETE
        "start.bat",
        "run_bot.bat",
        "start_unified_system.bat",
        "start_unified_system.ps1",
        "start_autonomous_development.bat",
        "start_autonomous_development.ps1", 
        "start_autonomous_development.sh",
        "start_profitable_trading.bat",
        
        # Deploy/setup - DELETE
        "deploy.py",
        "setup_autonomous_development.py",
        
        # Production/demo - DELETE
        "production_safety_system.py",
        "profit_generation_demo.py",
        "test_live_trading_now.py",
        
        # Monitor systems - DELETE AS ENTRY POINTS
        "monitor_system_status.py",
        "monitor_live_trading.py",
        "monitor_live_system.py",
        "monitor_system.py", 
        "monitor_account_balance.py",
        "real_time_monitor.py",
        
        # Status/reporting - DELETE
        "system_status.py",
        "system_status_report.py",
        
        # Diagnostics - DELETE
        "comprehensive_trading_diagnostic.py",
        "final_verification.py",
        "autonomous_system_debugger.py",
    ]
    
    deleted_count = 0
    not_found_count = 0
    
    for filename in obsolete_files:
        if delete_file_if_exists(filename):
            deleted_count += 1
        else:
            not_found_count += 1
    
    print("\n" + "🗑️" * 60)
    print("DELETION RESULTS:")
    print(f"✅ Files DELETED: {deleted_count}")
    print(f"⚠️  Files NOT FOUND: {not_found_count}")
    print("🗑️" * 60)
    
    # Verify only main.py exists as entry point
    main_files = []
    for file in os.listdir("."):
        if file.startswith("main") and file.endswith(".py"):
            main_files.append(file)
    
    print(f"\n📋 REMAINING MAIN FILES: {main_files}")
    
    if main_files == ["main.py"]:
        print("🎯 SUCCESS: ONLY main.py remains as entry point!")
        print("🚀 SYSTEM IS NOW READY FOR SINGLE ENTRY POINT OPERATION!")
    else:
        print("⚠️  WARNING: Unexpected main files found!")
    
    print("\n" + "🎉" * 60)
    print("OBSOLETE ENTRY POINT DELETION COMPLETED!")
    print("ONLY main.py IS NOW THE SINGLE ENTRY POINT!")
    print("🎉" * 60)

if __name__ == "__main__":
    main()
