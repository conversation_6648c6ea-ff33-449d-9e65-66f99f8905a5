#!/usr/bin/env python3
"""
Comprehensive System Diagnostic
This script will identify all system issues and create a reconstruction plan
"""
import sys
import os
import subprocess
import platform
from pathlib import Path
import json
from datetime import datetime

def write_log(message, level="INFO"):
    """Write diagnostic log to file"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_entry = f"[{timestamp}] [{level}] {message}\n"
    
    with open("diagnostic_log.txt", "a", encoding="utf-8") as f:
        f.write(log_entry)
    
    print(f"[{level}] {message}")

def check_system_info():
    """Check basic system information"""
    write_log("=== SYSTEM INFORMATION ===")
    write_log(f"Platform: {platform.platform()}")
    write_log(f"Python Version: {sys.version}")
    write_log(f"Python Executable: {sys.executable}")
    write_log(f"Current Working Directory: {os.getcwd()}")
    write_log(f"PATH: {os.environ.get('PATH', 'NOT_SET')}")

def check_python_installations():
    """Check for existing Python installations"""
    write_log("=== PYTHON INSTALLATIONS ===")
    
    # Common Python installation paths
    python_paths = [
        "C:\\Python39\\python.exe",
        "C:\\Python310\\python.exe", 
        "C:\\Python311\\python.exe",
        "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\python.exe",
        "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python.exe",
        "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python.exe",
    ]
    
    for path in python_paths:
        if Path(path).exists():
            write_log(f"Found Python: {path}")
        else:
            write_log(f"Not found: {path}")

def check_conda_installations():
    """Check for existing conda installations"""
    write_log("=== CONDA INSTALLATIONS ===")
    
    conda_paths = [
        "E:\\conda\\Miniconda3",
        "C:\\Users\\<USER>\\miniconda3",
        "C:\\Users\\<USER>\\anaconda3",
        "C:\\ProgramData\\Miniconda3",
        "C:\\ProgramData\\Anaconda3"
    ]
    
    for path in conda_paths:
        if Path(path).exists():
            write_log(f"Found conda: {path}")
            # Check for conda executable
            conda_exe = Path(path) / "Scripts" / "conda.exe"
            if conda_exe.exists():
                write_log(f"  Conda executable: {conda_exe}")
            else:
                write_log(f"  Missing conda.exe in {path}")
        else:
            write_log(f"Not found: {path}")

def check_environment_variables():
    """Check critical environment variables"""
    write_log("=== ENVIRONMENT VARIABLES ===")
    
    critical_vars = [
        "BYBIT_API_KEY",
        "BYBIT_API_SECRET", 
        "OPENAI_API_KEY",
        "ANTHROPIC_API_KEY",
        "CONDA_DEFAULT_ENV",
        "VIRTUAL_ENV"
    ]
    
    for var in critical_vars:
        value = os.environ.get(var)
        if value:
            if "API" in var:
                write_log(f"{var}: {value[:8]}... (masked)")
            else:
                write_log(f"{var}: {value}")
        else:
            write_log(f"{var}: NOT SET")

def check_current_directory_structure():
    """Check current directory structure"""
    write_log("=== CURRENT DIRECTORY STRUCTURE ===")
    
    current_dir = Path(".")
    write_log(f"Current directory: {current_dir.absolute()}")
    
    # Check for key files
    key_files = [
        "main.py",
        ".env",
        "requirements.txt",
        "config.yaml",
        "bybit_trading_bot.db"
    ]
    
    for file in key_files:
        if Path(file).exists():
            write_log(f"Found: {file}")
        else:
            write_log(f"Missing: {file}")
    
    # Check for key directories
    key_dirs = [
        "bybit_bot",
        "logs",
        "config",
        "data"
    ]
    
    for dir_name in key_dirs:
        if Path(dir_name).exists():
            write_log(f"Found directory: {dir_name}")
        else:
            write_log(f"Missing directory: {dir_name}")

def test_subprocess_execution():
    """Test if subprocess execution works"""
    write_log("=== SUBPROCESS TESTING ===")
    
    try:
        # Test basic command
        result = subprocess.run(["echo", "test"], capture_output=True, text=True, timeout=5)
        write_log(f"Echo test - Return code: {result.returncode}")
        write_log(f"Echo test - Output: {result.stdout.strip()}")
    except Exception as e:
        write_log(f"Echo test failed: {e}", "ERROR")
    
    try:
        # Test Python version
        result = subprocess.run([sys.executable, "--version"], capture_output=True, text=True, timeout=5)
        write_log(f"Python version test - Return code: {result.returncode}")
        write_log(f"Python version test - Output: {result.stdout.strip()}")
    except Exception as e:
        write_log(f"Python version test failed: {e}", "ERROR")

def create_reconstruction_plan():
    """Create a reconstruction plan based on findings"""
    write_log("=== RECONSTRUCTION PLAN ===")
    
    plan = {
        "timestamp": datetime.now().isoformat(),
        "steps": [
            {
                "phase": "1",
                "name": "Download Miniconda3",
                "description": "Download Miniconda3-latest-Windows-x86_64.exe to E:\\conda\\",
                "url": "https://repo.anaconda.com/miniconda/Miniconda3-latest-Windows-x86_64.exe",
                "target_path": "E:\\conda\\Miniconda3-latest-Windows-x86_64.exe"
            },
            {
                "phase": "2", 
                "name": "Install Miniconda3",
                "description": "Install to E:\\conda\\miniconda3",
                "command": "Miniconda3-latest-Windows-x86_64.exe /InstallationType=JustMe /RegisterPython=0 /S /D=E:\\conda\\miniconda3"
            },
            {
                "phase": "3",
                "name": "Create Environment",
                "description": "Create bybit-trader environment",
                "command": "E:\\conda\\miniconda3\\Scripts\\conda.exe create -n bybit-trader python=3.11 -y"
            },
            {
                "phase": "4",
                "name": "Install Dependencies",
                "description": "Install all required packages",
                "commands": [
                    "pip install aiohttp>=3.8.0 websockets>=11.0 pandas>=2.0.0 numpy>=1.24.0",
                    "pip install redis>=4.5.0 python-dotenv>=1.0.0 pyyaml>=6.0",
                    "pip install ccxt>=4.0.0 pybit>=5.6.0 requests>=2.31.0",
                    "pip install tensorflow>=2.13.0 scikit-learn>=1.3.0"
                ]
            }
        ]
    }
    
    with open("reconstruction_plan.json", "w") as f:
        json.dump(plan, f, indent=2)
    
    write_log("Reconstruction plan saved to reconstruction_plan.json")

def main():
    """Main diagnostic function"""
    # Clear previous log
    if Path("diagnostic_log.txt").exists():
        Path("diagnostic_log.txt").unlink()
    
    write_log("COMPREHENSIVE SYSTEM DIAGNOSTIC STARTING")
    write_log("=" * 50)
    
    try:
        check_system_info()
        check_python_installations()
        check_conda_installations()
        check_environment_variables()
        check_current_directory_structure()
        test_subprocess_execution()
        create_reconstruction_plan()
        
        write_log("=" * 50)
        write_log("DIAGNOSTIC COMPLETE - Check diagnostic_log.txt for full results")
        
    except Exception as e:
        write_log(f"DIAGNOSTIC FAILED: {e}", "ERROR")
        import traceback
        write_log(f"Traceback: {traceback.format_exc()}", "ERROR")

if __name__ == "__main__":
    main()
