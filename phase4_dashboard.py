#!/usr/bin/env python3
"""
PHASE 4: REAL-TIME MONITORING DASHBOARD
Live monitoring dashboard for 24-hour system validation
ZERO TOLERANCE: No errors, warnings, or compromises
"""

import os
import time
import sqlite3
import requests
import psutil
from datetime import datetime, timedelta
from dotenv import load_dotenv

class Phase4Dashboard:
    """Real-time monitoring dashboard for Phase 4 validation"""
    
    def __init__(self):
        load_dotenv()
        self.start_time = datetime.now()
        self.target_runtime = 24 * 60 * 60  # 24 hours
        self.api_key = os.getenv('BYBIT_API_KEY')
        self.base_url = 'https://api.bybit.com'
        
        # Performance targets
        self.targets = {
            'daily_profit': 15000.0,
            'hourly_profit': 625.0,
            'minute_profit': 10.42,
            'trades_per_hour': 100,
            'success_rate_min': 70.0
        }
    
    def clear_screen(self):
        """Clear terminal screen"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def get_system_metrics(self):
        """Collect real-time system metrics"""
        # System performance
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Trading processes
        trading_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'memory_info', 'cpu_percent']):
            try:
                if proc.info['name'] == 'python.exe' and proc.info['cmdline']:
                    cmdline = ' '.join(proc.info['cmdline'])
                    if 'main.py' in cmdline:
                        memory_mb = proc.info['memory_info'].rss / 1024 / 1024
                        trading_processes.append({
                            'pid': proc.info['pid'],
                            'memory_mb': memory_mb,
                            'cpu_percent': proc.info['cpu_percent'] or 0
                        })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        # API status
        api_status = self.check_api_status()
        
        # Database metrics
        db_metrics = self.get_database_metrics()
        
        # Uptime calculation
        uptime_seconds = (datetime.now() - self.start_time).total_seconds()
        
        return {
            'timestamp': datetime.now(),
            'uptime_seconds': uptime_seconds,
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'memory_used_gb': memory.used / 1024 / 1024 / 1024,
            'disk_percent': disk.percent,
            'trading_processes': trading_processes,
            'api_status': api_status,
            'db_metrics': db_metrics
        }
    
    def check_api_status(self):
        """Check Bybit API status"""
        try:
            start_time = time.time()
            response = requests.get(f'{self.base_url}/v5/market/time', timeout=5)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                return {'status': 'HEALTHY', 'response_time': response_time}
            elif response.status_code == 403:
                return {'status': 'RATE_LIMITED', 'response_time': response_time}
            else:
                return {'status': f'ERROR_{response.status_code}', 'response_time': response_time}
        except Exception as e:
            return {'status': 'UNREACHABLE', 'error': str(e)}
    
    def get_database_metrics(self):
        """Get database performance metrics"""
        try:
            if not os.path.exists('bybit_trading_bot.db'):
                return {'status': 'NOT_FOUND'}
            
            conn = sqlite3.connect('bybit_trading_bot.db')
            cursor = conn.cursor()
            
            # Total trades
            cursor.execute('SELECT COUNT(*) FROM trades')
            total_trades = cursor.fetchone()[0]
            
            # Recent trades (last hour)
            cursor.execute('SELECT COUNT(*) FROM trades WHERE timestamp > datetime("now", "-1 hour")')
            recent_trades = cursor.fetchone()[0]
            
            # Total profit
            cursor.execute('SELECT SUM(profit) FROM trades WHERE profit IS NOT NULL')
            profit_result = cursor.fetchone()
            total_profit = profit_result[0] if profit_result[0] else 0.0
            
            # Database size
            db_size = os.path.getsize('bybit_trading_bot.db') / 1024 / 1024  # MB
            
            conn.close()
            
            return {
                'status': 'OPERATIONAL',
                'total_trades': total_trades,
                'recent_trades': recent_trades,
                'total_profit': total_profit,
                'size_mb': db_size
            }
        except Exception as e:
            return {'status': 'ERROR', 'error': str(e)}
    
    def format_uptime(self, seconds):
        """Format uptime in human readable format"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        return f"{hours:02d}:{minutes:02d}:{secs:02d}"
    
    def get_progress_bar(self, current, target, width=50):
        """Generate ASCII progress bar"""
        progress = min(current / target, 1.0)
        filled = int(width * progress)
        bar = '█' * filled + '░' * (width - filled)
        return f"[{bar}] {progress*100:.1f}%"
    
    def display_dashboard(self, metrics):
        """Display real-time dashboard"""
        self.clear_screen()
        
        # Header
        print("=" * 80)
        print("PHASE 4: 24-HOUR SYSTEM VALIDATION - REAL-TIME DASHBOARD")
        print("=" * 80)
        print(f"Timestamp: {metrics['timestamp']}")
        print(f"Uptime: {self.format_uptime(metrics['uptime_seconds'])}")
        
        # Progress toward 24-hour target
        progress = metrics['uptime_seconds'] / self.target_runtime
        progress_bar = self.get_progress_bar(metrics['uptime_seconds'], self.target_runtime)
        print(f"24-Hour Progress: {progress_bar}")
        print()
        
        # System Performance
        print("SYSTEM PERFORMANCE:")
        print(f"  CPU Usage:    {metrics['cpu_percent']:6.1f}% {'⚠️ HIGH' if metrics['cpu_percent'] > 80 else '✅ OK'}")
        print(f"  Memory Usage: {metrics['memory_percent']:6.1f}% ({metrics['memory_used_gb']:.1f}GB) {'⚠️ HIGH' if metrics['memory_percent'] > 85 else '✅ OK'}")
        print(f"  Disk Usage:   {metrics['disk_percent']:6.1f}% {'⚠️ HIGH' if metrics['disk_percent'] > 90 else '✅ OK'}")
        print()
        
        # Trading Processes
        print("TRADING PROCESSES:")
        if metrics['trading_processes']:
            total_memory = sum(p['memory_mb'] for p in metrics['trading_processes'])
            total_cpu = sum(p['cpu_percent'] for p in metrics['trading_processes'])
            print(f"  Active Processes: {len(metrics['trading_processes'])}")
            print(f"  Total Memory:     {total_memory:.1f}MB")
            print(f"  Total CPU:        {total_cpu:.1f}%")
            
            # Show top 5 processes by memory
            top_processes = sorted(metrics['trading_processes'], key=lambda x: x['memory_mb'], reverse=True)[:5]
            for i, proc in enumerate(top_processes, 1):
                print(f"    {i}. PID {proc['pid']}: {proc['memory_mb']:6.1f}MB, {proc['cpu_percent']:5.1f}% CPU")
        else:
            print("  No trading processes found ❌ CRITICAL")
        print()
        
        # API Status
        print("API STATUS:")
        api = metrics['api_status']
        if api['status'] == 'HEALTHY':
            print(f"  Status: ✅ {api['status']} (Response: {api['response_time']:.3f}s)")
        elif api['status'] == 'RATE_LIMITED':
            print(f"  Status: ⚠️ {api['status']} (Response: {api['response_time']:.3f}s)")
        else:
            print(f"  Status: ❌ {api['status']}")
        print()
        
        # Database Status
        print("DATABASE STATUS:")
        db = metrics['db_metrics']
        if db['status'] == 'OPERATIONAL':
            print(f"  Status: ✅ {db['status']}")
            print(f"  Total Trades:   {db['total_trades']}")
            print(f"  Recent Trades:  {db['recent_trades']} (last hour)")
            print(f"  Total Profit:   ${db['total_profit']:.2f}")
            print(f"  Database Size:  {db['size_mb']:.1f}MB")
        else:
            print(f"  Status: ❌ {db['status']}")
        print()
        
        # Profit Tracking
        print("PROFIT TRACKING:")
        if db['status'] == 'OPERATIONAL':
            hours_running = metrics['uptime_seconds'] / 3600
            if hours_running > 0:
                profit_per_hour = db['total_profit'] / hours_running
                daily_projection = profit_per_hour * 24
                target_achievement = (daily_projection / self.targets['daily_profit']) * 100
                
                print(f"  Current Profit:     ${db['total_profit']:.2f}")
                print(f"  Profit/Hour:        ${profit_per_hour:.2f}")
                print(f"  Daily Projection:   ${daily_projection:.2f}")
                print(f"  Target Achievement: {target_achievement:.1f}% of ${self.targets['daily_profit']:.0f}")
                
                if target_achievement >= 100:
                    print("  Status: ✅ ON TARGET")
                elif target_achievement >= 50:
                    print("  Status: ⚠️ BELOW TARGET")
                else:
                    print("  Status: ❌ CRITICAL - FAR BELOW TARGET")
            else:
                print("  Status: ⏳ STARTING UP")
        print()
        
        # Validation Status
        print("VALIDATION STATUS:")
        validation_items = [
            ("System Uptime", "✅ RUNNING" if metrics['trading_processes'] else "❌ STOPPED"),
            ("API Connectivity", "✅ OK" if api['status'] in ['HEALTHY', 'RATE_LIMITED'] else "❌ FAILED"),
            ("Database Operations", "✅ OK" if db['status'] == 'OPERATIONAL' else "❌ FAILED"),
            ("Memory Usage", "✅ OK" if metrics['memory_percent'] < 90 else "❌ CRITICAL"),
            ("CPU Usage", "✅ OK" if metrics['cpu_percent'] < 80 else "❌ HIGH"),
        ]
        
        for item, status in validation_items:
            print(f"  {item:20}: {status}")
        
        print()
        print("=" * 80)
        print("Press Ctrl+C to stop monitoring")
        print("=" * 80)
    
    def run(self):
        """Main dashboard loop"""
        print("Starting Phase 4 Real-Time Dashboard...")
        print("Monitoring system for 24-hour validation...")
        print()
        
        try:
            while True:
                metrics = self.get_system_metrics()
                self.display_dashboard(metrics)
                
                # Check if 24 hours completed
                if metrics['uptime_seconds'] >= self.target_runtime:
                    print("\n🎉 24-HOUR VALIDATION COMPLETED SUCCESSFULLY! 🎉")
                    break
                
                # Update every 30 seconds
                time.sleep(30)
                
        except KeyboardInterrupt:
            print("\nDashboard stopped by user")
        except Exception as e:
            print(f"\nDashboard error: {e}")

if __name__ == "__main__":
    dashboard = Phase4Dashboard()
    dashboard.run()
