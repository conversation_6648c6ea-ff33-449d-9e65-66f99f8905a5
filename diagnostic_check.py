#!/usr/bin/env python3
"""
DIAGNOSTIC CHECK - VERIFY ACTUAL SYSTEM STATE
"""
import sqlite3
import os
import sys
from pathlib import Path

def check_imports():
    """Check if critical imports work"""
    print("CHECKING CRITICAL IMPORTS...")

    import_results = {}

    # Test core imports
    try:
        from bybit_bot.core.config import EnhancedBotConfig
        import_results['config'] = True
        print("SUCCESS: EnhancedBotConfig import")
    except Exception as e:
        import_results['config'] = False
        print(f"FAILED: EnhancedBotConfig import - {e}")

    try:
        from bybit_bot.database.connection import DatabaseManager
        import_results['database'] = True
        print("SUCCESS: DatabaseManager import")
    except Exception as e:
        import_results['database'] = False
        print(f"FAILED: DatabaseManager import - {e}")

    try:
        from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
        import_results['bybit_client'] = True
        print("SUCCESS: EnhancedBybitClient import")
    except Exception as e:
        import_results['bybit_client'] = False
        print(f"FAILED: EnhancedBybitClient import - {e}")

    return import_results

def check_sqlite_database():
    """Check SQLite database actual state"""
    print("\nCHECKING SQLITE DATABASE...")
    db_path = "bybit_trading_bot.db"

    if not Path(db_path).exists():
        print("ERROR: SQLite database file does not exist")
        return False

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [t[0] for t in cursor.fetchall()]
        print(f"Tables found: {len(tables)}")

        # Check key tables for data
        key_tables = ['trades', 'positions', 'cognitive_metrics', 'ai_system_interactions']

        for table in key_tables:
            if table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"{table}: {count} records")
            else:
                print(f"{table}: TABLE MISSING")

        conn.close()
        return True

    except Exception as e:
        print(f"ERROR checking SQLite: {e}")
        return False

def check_redis_database():
    """Check Redis database actual state"""
    print("\nCHECKING REDIS DATABASE...")
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, decode_responses=True)

        # Test connection
        r.ping()
        print("Redis connection: SUCCESS")

        # Check database size
        db_size = r.dbsize()
        print(f"Redis keys count: {db_size}")

        return True

    except Exception as e:
        print(f"ERROR checking Redis: {e}")
        return False

def check_environment():
    """Check environment variables"""
    print("\nCHECKING ENVIRONMENT...")

    # Check environment variables
    api_key = os.getenv('BYBIT_API_KEY')
    api_secret = os.getenv('BYBIT_API_SECRET')

    print(f"API Key present: {bool(api_key)}")
    print(f"API Secret present: {bool(api_secret)}")

    if api_key:
        print(f"API Key (masked): {api_key[:8]}...")

    return bool(api_key and api_secret)

def test_system_initialization():
    """Test actual system initialization"""
    print("\nTESTING SYSTEM INITIALIZATION...")

    try:
        # Load environment
        from dotenv import load_dotenv
        load_dotenv()
        print("SUCCESS: Environment loaded")

        # Test config creation
        from bybit_bot.core.config import EnhancedBotConfig
        config = EnhancedBotConfig()
        print("SUCCESS: Config created")

        # Test database creation
        from bybit_bot.database.connection import DatabaseManager
        db_manager = DatabaseManager(config)
        print("SUCCESS: Database manager created")

        return True

    except Exception as e:
        print(f"FAILED: System initialization test - {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def main():
    """Main diagnostic function"""
    print("SYSTEM DIAGNOSTIC CHECK - ACTUAL STATE VERIFICATION")
    print("=" * 60)

    # Test imports first
    import_results = check_imports()

    # Test other components
    results = {
        'imports': all(import_results.values()),
        'sqlite': check_sqlite_database(),
        'redis': check_redis_database(),
        'environment': check_environment(),
        'initialization': test_system_initialization()
    }

    print("\n" + "=" * 60)
    print("DIAGNOSTIC SUMMARY:")
    print(f"Critical Imports: {'WORKING' if results['imports'] else 'FAILED'}")
    print(f"SQLite Database: {'OPERATIONAL' if results['sqlite'] else 'FAILED'}")
    print(f"Redis Database: {'OPERATIONAL' if results['redis'] else 'FAILED'}")
    print(f"Environment: {'CONFIGURED' if results['environment'] else 'MISSING'}")
    print(f"System Init: {'WORKING' if results['initialization'] else 'FAILED'}")

    # Overall status
    operational_count = sum(results.values())
    print(f"\nOVERALL STATUS: {operational_count}/5 components operational")

    if operational_count < 5:
        print("WARNING: System is NOT fully operational despite logs claiming otherwise")
        print("IDENTIFIED ISSUES:")
        for component, status in results.items():
            if not status:
                print(f"  - {component.upper()}: FAILED")
    else:
        print("SUCCESS: System appears to be genuinely operational")

if __name__ == "__main__":
    main()
