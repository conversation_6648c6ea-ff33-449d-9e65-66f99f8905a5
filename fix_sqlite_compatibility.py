#!/usr/bin/env python3
"""
SQLite Compatibility Fix - Replace NOW() with CURRENT_TIMESTAMP
"""
import os
import re
from pathlib import Path

def fix_sqlite_compatibility():
    """Fix SQLite NOW() function compatibility issues"""
    
    print("SQLite COMPATIBILITY FIX")
    print("=" * 30)
    
    # Change to correct directory
    try:
        os.chdir(r"e:\The_real_deal_copy\Bybit_Bot\BOT")
        print(f"Working directory: {os.getcwd()}")
    except Exception as e:
        print(f"ERROR changing directory: {e}")
        return False
    
    # Files that need fixing based on grep results
    files_to_fix = [
        "bybit_bot/ai/memory_manager.py",
        "bybit_bot/ai/advanced_memory_system.py", 
        "bybit_bot/data_crawler/huggingface_integration.py",
        "bybit_bot/core/bot_manager.py"
    ]
    
    fixed_count = 0
    total_files = len(files_to_fix)
    
    for file_path_str in files_to_fix:
        file_path = Path(file_path_str)
        
        if not file_path.exists():
            print(f"SKIP: {file_path} not found")
            continue
        
        print(f"\nProcessing: {file_path}")
        
        try:
            # Read file content
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Count NOW() occurrences
            now_count = content.count("NOW()")
            
            if now_count == 0:
                print(f"  No NOW() functions found")
                continue
            
            print(f"  Found {now_count} NOW() functions to fix")
            
            # Replace NOW() with CURRENT_TIMESTAMP for SQLite compatibility
            # Pattern 1: updated_at = NOW()
            content = re.sub(
                r'updated_at\s*=\s*NOW\(\)', 
                'updated_at = CURRENT_TIMESTAMP', 
                content
            )
            
            # Pattern 2: , updated_at = NOW()
            content = re.sub(
                r',\s*updated_at\s*=\s*NOW\(\)', 
                ', updated_at = CURRENT_TIMESTAMP', 
                content
            )
            
            # Pattern 3: SET ... updated_at = NOW()
            def replace_now(match):
                return match.group(0).replace('NOW()', 'CURRENT_TIMESTAMP')
            
            content = re.sub(
                r'SET\s+([^=]+\s*=\s*[^,]+,\s*)*updated_at\s*=\s*NOW\(\)', 
                replace_now, 
                content
            )
            
            # General pattern: any NOW() function
            content = re.sub(r'NOW\(\)', 'CURRENT_TIMESTAMP', content)
            
            # Write back the fixed content
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"  ✓ Fixed {now_count} NOW() functions")
            fixed_count += 1
            
        except Exception as e:
            print(f"  ✗ Error fixing {file_path}: {e}")
    
    print(f"\nSQLite Compatibility Fix Results:")
    print(f"Files processed: {fixed_count}/{total_files}")
    
    if fixed_count == total_files:
        print("✅ ALL FILES FIXED SUCCESSFULLY!")
        return True
    else:
        print(f"⚠️  {total_files - fixed_count} files had issues")
        return False

if __name__ == "__main__":
    print("BYBIT BOT - SQLite Compatibility Fix")
    print("Replacing NOW() with CURRENT_TIMESTAMP for SQLite...")
    
    success = fix_sqlite_compatibility()
    
    if success:
        print("\n🎉 SQLite COMPATIBILITY FIX COMPLETED!")
        print("Database errors should now be resolved!")
    else:
        print("\n⚠️  SQLite compatibility fix had some issues")
    
    print("\nRestart the bot to apply fixes:")
    print("python main.py")
