#!/usr/bin/env python3
"""
Debug configuration and Bybit client initialization
"""

from dotenv import load_dotenv
import os
import sys
import asyncio

# Load environment variables
load_dotenv()

async def test_config_initialization():
    """Test configuration initialization"""
    try:
        print("TESTING CONFIGURATION INITIALIZATION")
        print("=" * 50)
        
        # Test environment variables first
        print("1. ENVIRONMENT VARIABLES:")
        api_key = os.getenv('BYBIT_API_KEY')
        api_secret = os.getenv('BYBIT_API_SECRET')
        testnet = os.getenv('BYBIT_TESTNET')
        live_trading = os.getenv('ENABLE_LIVE_TRADING')
        
        print(f"   BYBIT_API_KEY: {api_key[:10] + '...' if api_key else 'NOT_SET'}")
        print(f"   BYBIT_API_SECRET: {api_secret[:10] + '...' if api_secret else 'NOT_SET'}")
        print(f"   BYBIT_TESTNET: {testnet}")
        print(f"   ENABLE_LIVE_TRADING: {live_trading}")
        
        if not api_key or not api_secret:
            print("ERROR: API keys not found in environment")
            return False
            
        # Test config creation
        print("\n2. ENHANCED BOT CONFIG:")
        from bybit_bot.core.config import EnhancedBotConfig
        
        config = EnhancedBotConfig()
        print(f"   Config created: {config is not None}")
        print(f"   Config type: {type(config)}")
        
        # Check API keys in config
        print(f"   Has api_keys attribute: {hasattr(config, 'api_keys')}")
        if hasattr(config, 'api_keys'):
            print(f"   API keys object: {config.api_keys}")
            print(f"   Bybit keys: {config.api_keys.bybit}")
            
            bybit_api_key = config.api_keys.bybit.get('api_key')
            bybit_api_secret = config.api_keys.bybit.get('api_secret')
            
            print(f"   Bybit API key in config: {bybit_api_key[:10] + '...' if bybit_api_key else 'NOT_SET'}")
            print(f"   Bybit API secret in config: {bybit_api_secret[:10] + '...' if bybit_api_secret else 'NOT_SET'}")
            
            if bybit_api_key and bybit_api_secret:
                print("   SUCCESS: Bybit API keys loaded in config")
                
                # Test Bybit client creation
                print("\n3. ENHANCED BYBIT CLIENT:")
                from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
                
                client = EnhancedBybitClient(config)
                print(f"   Client created: {client is not None}")
                
                # Test client initialization
                print("   Initializing client...")
                await client.initialize()
                print("   Client initialized successfully")
                
                # Test API connection
                print("   Testing API connection...")
                balance = await client.get_wallet_balance()
                print(f"   Balance retrieved: {balance is not None}")
                
                if balance and 'list' in balance:
                    account_info = balance['list'][0]
                    total_equity = float(account_info.get('totalEquity', 0))
                    print(f"   Total Equity: ${total_equity:.2f}")
                    print("   SUCCESS: Full API connection working")
                    return True
                else:
                    print("   ERROR: Could not retrieve balance")
                    return False
            else:
                print("   ERROR: API keys not found in config")
                return False
        else:
            print("   ERROR: No api_keys attribute in config")
            return False
            
    except Exception as e:
        print(f"ERROR in config test: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_main_system_initialization():
    """Test main system initialization"""
    try:
        print("\n" + "=" * 50)
        print("TESTING MAIN SYSTEM INITIALIZATION")
        print("=" * 50)
        
        from main import BybitTradingBotSystem
        
        print("1. Creating main system...")
        bot = BybitTradingBotSystem()
        print(f"   System created: {bot is not None}")
        
        print("2. Testing system initialization...")
        success = await bot.initialize_all_systems()
        print(f"   Initialization result: {success}")
        
        if success:
            print("3. Checking Bybit client...")
            if hasattr(bot, 'bybit_client') and bot.bybit_client:
                print("   SUCCESS: Bybit client initialized in main system")
                
                # Test a simple API call
                balance = await bot.bybit_client.get_wallet_balance()
                if balance:
                    print("   SUCCESS: API call from main system works")
                    return True
                else:
                    print("   ERROR: API call from main system failed")
                    return False
            else:
                print("   ERROR: Bybit client not initialized in main system")
                return False
        else:
            print("   ERROR: System initialization failed")
            return False
            
    except Exception as e:
        print(f"ERROR in main system test: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("CONFIGURATION AND CLIENT DEBUG TEST")
    print("=" * 60)
    
    # Test 1: Config initialization
    config_test = await test_config_initialization()
    
    # Test 2: Main system initialization
    main_test = await test_main_system_initialization()
    
    print("\n" + "=" * 60)
    print("TEST RESULTS:")
    print(f"Config Test: {'PASSED' if config_test else 'FAILED'}")
    print(f"Main System Test: {'PASSED' if main_test else 'FAILED'}")
    
    if config_test and main_test:
        print("OVERALL RESULT: ALL TESTS PASSED - READY FOR TRADING")
        return True
    else:
        print("OVERALL RESULT: ISSUES DETECTED - NEEDS FIXING")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        if result:
            print("SUCCESS: All tests passed")
            sys.exit(0)
        else:
            print("FAILURE: Some tests failed")
            sys.exit(1)
    except Exception as e:
        print(f"FATAL ERROR: {e}")
        sys.exit(1)
