#!/usr/bin/env python3
"""
API Signature Test - Verify Bybit API authentication
"""

import asyncio
import aiohttp
import hashlib
import hmac
import json
import time
from urllib.parse import urlencode
import yaml

async def test_bybit_api():
    """Test Bybit API signature generation and authentication"""
    
    # Load config
    with open('config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    api_key = config['bybit']['api_key']
    api_secret = config['bybit']['api_secret']
    base_url = config['bybit']['base_url']
    recv_window = config['bybit']['recv_window']
    
    print(f"[OK] Using API Key: {api_key[:8]}...")
    print(f"[OK] Base URL: {base_url}")
    print(f"[OK] Recv Window: {recv_window}")
    
    # Test 1: Server time (no auth required)
    print("\n=== TEST 1: Server Time (No Auth) ===")
    async with aiohttp.ClientSession() as session:
        url = f"{base_url}/v5/market/time"
        try:
            async with session.get(url) as response:
                data = await response.json()
                print(f"Status: {response.status}")
                print(f"Response: {data}")
                if data.get("retCode") == 0:
                    print("[OK] Server time test passed")
                else:
                    print(f"[ERROR] Server time test failed: {data}")
        except Exception as e:
            print(f"[ERROR] Server time test failed: {e}")
    
    # Test 2: Account info (auth required)
    print("\n=== TEST 2: Account Info (Auth Required) ===")
    async with aiohttp.ClientSession() as session:
        endpoint = "/v5/account/wallet-balance"
        timestamp = str(int(time.time() * 1000))
        
        # Parameters for signed request
        params = {
            "accountType": "UNIFIED",
            "timestamp": timestamp,
            "recv_window": str(recv_window)
        }
        
        # Create signature
        sorted_params = sorted(params.items())
        query_string = urlencode(sorted_params)
        signing_string = f"{timestamp}{api_key}{recv_window}{query_string}"
        
        print(f"Signing string: {signing_string}")
        
        signature = hmac.new(
            api_secret.encode('utf-8'),
            signing_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        print(f"Generated signature: {signature}")
        
        # Headers
        headers = {
            "Content-Type": "application/json",
            "X-BAPI-API-KEY": api_key,
            "X-BAPI-SIGN": signature,
            "X-BAPI-TIMESTAMP": timestamp,
            "X-BAPI-RECV-WINDOW": str(recv_window)
        }
        
        # Make request
        url = f"{base_url}{endpoint}?{query_string}"
        print(f"Request URL: {url}")
        
        try:
            async with session.get(url, headers=headers) as response:
                data = await response.json()
                print(f"Status: {response.status}")
                print(f"Response: {json.dumps(data, indent=2)}")
                
                if data.get("retCode") == 0:
                    print("[OK] Account info test passed")
                    if 'result' in data and 'list' in data['result']:
                        for wallet in data['result']['list']:
                            print(f"Account Type: {wallet.get('accountType', 'N/A')}")
                            for coin in wallet.get('coin', []):
                                if float(coin.get('walletBalance', 0)) > 0:
                                    print(f"  {coin['coin']}: {coin['walletBalance']}")
                else:
                    print(f"[ERROR] Account info test failed: {data}")
                    if data.get("retCode") == 10004:
                        print("[ERROR] Signature error - check API credentials")
                    elif data.get("retCode") == 10003:
                        print("[ERROR] API key permissions insufficient")
        except Exception as e:
            print(f"[ERROR] Account info test failed: {e}")
    
    # Test 3: Get ticker (no auth required)
    print("\n=== TEST 3: Get Ticker (No Auth) ===")
    async with aiohttp.ClientSession() as session:
        url = f"{base_url}/v5/market/tickers"
        params = {"category": "spot", "symbol": "BTCUSDT"}
        
        try:
            async with session.get(url, params=params) as response:
                data = await response.json()
                print(f"Status: {response.status}")
                print(f"Response: {json.dumps(data, indent=2)}")
                
                if data.get("retCode") == 0:
                    print("[OK] Ticker test passed")
                else:
                    print(f"[ERROR] Ticker test failed: {data}")
        except Exception as e:
            print(f"[ERROR] Ticker test failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_bybit_api())
