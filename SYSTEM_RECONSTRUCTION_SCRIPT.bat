@echo off
REM CRITICAL SYSTEM RECONSTRUCTION SCRIPT
REM This script will completely rebuild the Python environment for the Bybit trading bot

echo ========================================
echo BYBIT TRADING BOT SYSTEM RECONSTRUCTION
echo ========================================
echo.
echo PHASE 1: ENVIRONMENT CLEANUP AND SETUP
echo.

REM Create E:\conda directory if it doesn't exist
echo Creating conda directory structure...
if not exist "E:\conda" mkdir "E:\conda"
cd /d "E:\conda"

REM Download Miniconda3 installer
echo Downloading Miniconda3 installer...
powershell -Command "Invoke-WebRequest -Uri 'https://repo.anaconda.com/miniconda/Miniconda3-latest-Windows-x86_64.exe' -OutFile 'Miniconda3-latest-Windows-x86_64.exe'"

REM Check if download was successful
if not exist "Miniconda3-latest-Windows-x86_64.exe" (
    echo ERROR: Failed to download Miniconda3 installer
    echo Please manually download from: https://repo.anaconda.com/miniconda/Miniconda3-latest-Windows-x86_64.exe
    echo Save it to: E:\conda\Miniconda3-latest-Windows-x86_64.exe
    pause
    exit /b 1
)

echo.
echo PHASE 2: MINICONDA3 INSTALLATION
echo.

REM Install Miniconda3 silently
echo Installing Miniconda3 to E:\conda\miniconda3...
Miniconda3-latest-Windows-x86_64.exe /InstallationType=JustMe /RegisterPython=0 /S /D=E:\conda\miniconda3

REM Wait for installation to complete
timeout /t 30 /nobreak

REM Verify installation
if not exist "E:\conda\miniconda3\Scripts\conda.exe" (
    echo ERROR: Miniconda3 installation failed
    echo Please run the installer manually: E:\conda\Miniconda3-latest-Windows-x86_64.exe
    echo Install to: E:\conda\miniconda3
    pause
    exit /b 1
)

echo SUCCESS: Miniconda3 installed successfully

echo.
echo PHASE 3: ENVIRONMENT SETUP
echo.

REM Add conda to PATH for this session
set PATH=E:\conda\miniconda3;E:\conda\miniconda3\Scripts;E:\conda\miniconda3\Library\bin;%PATH%

REM Initialize conda
echo Initializing conda...
call "E:\conda\miniconda3\Scripts\conda.exe" init cmd.exe

REM Create bybit-trader environment
echo Creating bybit-trader environment with Python 3.11...
call "E:\conda\miniconda3\Scripts\conda.exe" create -n bybit-trader python=3.11 -y

REM Activate environment
echo Activating bybit-trader environment...
call "E:\conda\miniconda3\Scripts\activate.bat" bybit-trader

echo.
echo PHASE 4: DEPENDENCY INSTALLATION
echo.

REM Install core dependencies
echo Installing core dependencies...
pip install aiohttp>=3.8.0 websockets>=11.0 pandas>=2.0.0 numpy>=1.24.0 redis>=4.5.0 python-dotenv>=1.0.0 pyyaml>=6.0

REM Install trading dependencies
echo Installing trading dependencies...
pip install ccxt>=4.0.0 pybit>=5.6.0 requests>=2.31.0

REM Install AI dependencies
echo Installing AI dependencies...
pip install tensorflow>=2.13.0 scikit-learn>=1.3.0

REM Install additional dependencies
echo Installing additional dependencies...
pip install asyncio-mqtt psutil sqlalchemy aiosqlite

echo.
echo PHASE 5: VERIFICATION
echo.

REM Test Python installation
echo Testing Python installation...
python --version

REM Test conda environment
echo Testing conda environment...
conda info --envs

REM Test key imports
echo Testing key imports...
python -c "import aiohttp, websockets, pandas, numpy, redis, ccxt, tensorflow; print('All imports successful')"

echo.
echo ========================================
echo SYSTEM RECONSTRUCTION COMPLETE
echo ========================================
echo.
echo Next steps:
echo 1. Navigate to: e:\The_real_deal_copy\Bybit_Bot\BOT
echo 2. Activate environment: E:\conda\miniconda3\Scripts\activate.bat bybit-trader
echo 3. Run system test: python system_test.py
echo 4. Run main system: python main.py
echo.
echo Environment location: E:\conda\miniconda3\envs\bybit-trader
echo Python executable: E:\conda\miniconda3\envs\bybit-trader\python.exe
echo.
pause
