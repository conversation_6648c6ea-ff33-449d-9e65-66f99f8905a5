#!/usr/bin/env python3
"""
Test Ultra-Scalping Engine to verify it's finding opportunities
"""

import asyncio
import sys
sys.path.insert(0, '.')

from dotenv import load_dotenv
from bybit_bot.core.config import EnhancedBotConfig
from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
from bybit_bot.profit_maximization.advanced_profit_engine import AdvancedProfitEngine
from bybit_bot.database.connection import DatabaseManager

load_dotenv()

async def test_ultra_scalping():
    """Test if ultra-scalping engine finds opportunities"""
    print("TESTING ULTRA-SCALPING ENGINE")
    print("=" * 50)
    
    try:
        # Initialize components
        config = EnhancedBotConfig()
        bybit_client = EnhancedBybitClient(config)
        await bybit_client.initialize()
        
        db_manager = DatabaseManager(config)
        await db_manager.initialize()
        
        # Create profit engine
        profit_engine = AdvancedProfitEngine(config, bybit_client, db_manager)
        
        print("1. TESTING OPPORTUNITY DETECTION:")
        
        # Test with BTCUSDT
        symbol = "BTCUSDT"
        print(f"   Testing {symbol}...")
        
        # Get real market data
        price_data = await profit_engine._get_ultra_fast_price_data(symbol)
        if price_data:
            print(f"   Price data: {price_data}")
            
            # Test opportunity detection
            opportunity = await profit_engine._detect_scalping_opportunity(symbol, price_data)
            if opportunity:
                print(f"   OPPORTUNITY FOUND!")
                print(f"   - Strategy: {opportunity.strategy}")
                print(f"   - Expected Profit: ${opportunity.expected_profit:.6f}")
                print(f"   - Confidence: {opportunity.confidence:.2f}")
                print(f"   - Risk Score: {opportunity.risk_score:.2f}")
                print(f"   - Entry Price: ${opportunity.entry_price:.2f}")
                print(f"   - Target Price: ${opportunity.target_price:.2f}")
                print(f"   - Quantity: {opportunity.quantity:.6f}")
                print(f"   - Metadata: {opportunity.metadata}")
            else:
                print("   NO OPPORTUNITY FOUND - This is the problem!")
        else:
            print("   ERROR: Could not get price data")
            
        print("\n2. TESTING MULTIPLE SYMBOLS:")
        symbols = ["BTCUSDT", "ETHUSDT", "SOLUSDT"]
        opportunities_found = 0
        
        for symbol in symbols:
            try:
                price_data = await profit_engine._get_ultra_fast_price_data(symbol)
                if price_data:
                    opportunity = await profit_engine._detect_scalping_opportunity(symbol, price_data)
                    if opportunity:
                        opportunities_found += 1
                        print(f"   {symbol}: OPPORTUNITY FOUND (${opportunity.expected_profit:.6f})")
                    else:
                        print(f"   {symbol}: No opportunity")
                else:
                    print(f"   {symbol}: No price data")
            except Exception as e:
                print(f"   {symbol}: ERROR - {e}")
                
        print(f"\n3. SUMMARY:")
        print(f"   Opportunities found: {opportunities_found}/{len(symbols)}")
        
        if opportunities_found == 0:
            print("   PROBLEM: No opportunities found - detection too restrictive!")
        elif opportunities_found < len(symbols):
            print("   PARTIAL: Some opportunities found - could be more aggressive")
        else:
            print("   SUCCESS: Opportunities found for all symbols!")
            
        # Test position sizing
        print("\n4. TESTING POSITION SIZING:")
        test_price = 50000.0  # Example BTC price
        position_size = await profit_engine._calculate_scalping_size("BTCUSDT", test_price)
        print(f"   Position size for ${test_price}: {position_size:.6f} BTC")
        
        # Calculate USD value
        usd_value = position_size * test_price
        print(f"   USD value: ${usd_value:.2f}")
        
        if usd_value < 10:
            print("   WARNING: Position size too small for meaningful profit")
        elif usd_value > 1000:
            print("   WARNING: Position size might be too large")
        else:
            print("   GOOD: Position size is reasonable")
            
        await bybit_client.close()
        await db_manager.close()
        
    except Exception as e:
        print(f"ERROR in test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_ultra_scalping())
