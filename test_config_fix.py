#!/usr/bin/env python3
"""Test script to verify config fix"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from bybit_bot.core.config import EnhancedBotConfig
    print("✅ Successfully imported EnhancedBotConfig")
    
    config = EnhancedBotConfig()
    print("✅ Successfully created EnhancedBotConfig instance")
    
    # Test the property accessors we added
    print(f"✅ max_risk_percentage: {config.max_risk_percentage}")
    print(f"✅ max_drawdown_percentage: {config.max_drawdown_percentage}")
    print(f"✅ max_open_positions: {config.max_open_positions}")
    print(f"✅ min_order_size: {config.min_order_size}")
    
    # Test Risk Manager import
    try:
        from bybit_bot.ai.advanced_risk_manager import AdvancedRiskManager
        print("✅ Successfully imported AdvancedRiskManager from AI folder")
        
        # Test Risk Manager instantiation
        try:
            from bybit_bot.database.connection import DatabaseManager
            db_manager = DatabaseManager()
            
            risk_manager = AdvancedRiskManager(
                config=config,
                database_manager=db_manager,
                bybit_client=None  # Can be None for test
            )
            print("✅ Successfully created AdvancedRiskManager instance")
        except Exception as e2:
            print(f"❌ Failed to create AdvancedRiskManager: {e2}")
            
    except Exception as e:
        print(f"❌ Failed to import AdvancedRiskManager: {e}")
        
except Exception as e:
    print(f"❌ Failed to test config: {e}")
    import traceback
    traceback.print_exc()
