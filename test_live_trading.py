#!/usr/bin/env python3
"""
Test live trading functionality with actual Bybit API
"""

from dotenv import load_dotenv
import os
import sys
import asyncio

# Load environment variables
load_dotenv()

async def test_live_trading():
    """Test live trading functionality"""
    try:
        # Import the enhanced Bybit client and config
        from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
        from bybit_bot.core.config import EnhancedBotConfig

        print("Creating Enhanced Bot Config...")
        config = EnhancedBotConfig()

        print("Creating Enhanced Bybit Client...")
        client = EnhancedBybitClient(config)
        
        print("Testing account connection...")
        balance = await client.get_wallet_balance()
        print(f"Account balance retrieved: {type(balance)}")
        print(f"Balance response: {balance}")

        if balance and isinstance(balance, dict):
            if 'list' in balance and len(balance['list']) > 0:
                account_info = balance['list'][0]
                total_equity = float(account_info.get('totalEquity', 0))
                available_balance = float(account_info.get('totalAvailableBalance', 0))
                print(f"Total Equity: ${total_equity:.2f}")
                print(f"Available Balance: ${available_balance:.2f}")

                # Check if we have enough balance for trading
                if available_balance > 10:  # At least $10 available
                    print("SUFFICIENT BALANCE FOR TRADING")

                    # Test market data retrieval
                    print("Testing market data retrieval...")
                    try:
                        market_data = await client.get_market_data('BTCUSDT')
                        print(f"Market data retrieved for BTCUSDT: {type(market_data)}")

                        # Test position checking
                        print("Testing position retrieval...")
                        positions = await client.get_positions()
                        print(f"Positions retrieved: {type(positions)}")

                        print("LIVE TRADING TEST: READY FOR EXECUTION")
                        return True

                    except Exception as e:
                        print(f"Error testing market data/positions: {e}")
                        return False
                else:
                    print(f"INSUFFICIENT BALANCE: Only ${available_balance:.2f} available")
                    return False
            else:
                print(f"ERROR: Unexpected balance response structure: {balance}")
                return False
        else:
            print("ERROR: Could not retrieve account balance")
            return False
            
    except Exception as e:
        print(f"ERROR in live trading test: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_trading_system_startup():
    """Test if the main trading system can start properly"""
    try:
        print("Testing main trading system startup...")
        
        # Import the main system
        from main import BybitTradingBotSystem
        
        print("Creating trading bot system...")
        bot = BybitTradingBotSystem()
        
        print("Testing system initialization...")
        # Test if we can initialize core components
        if hasattr(bot, 'bybit_client') and bot.bybit_client:
            print("SUCCESS: Bybit client initialized")
        else:
            print("WARNING: Bybit client not initialized")
            
        print("TRADING SYSTEM STARTUP TEST: COMPLETED")
        return True
        
    except Exception as e:
        print(f"ERROR in trading system startup test: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("LIVE TRADING FUNCTIONALITY TEST")
    print("=" * 50)
    
    # Test 1: Live trading functionality
    print("\nTEST 1: Live Trading Functionality")
    trading_test = await test_live_trading()
    
    # Test 2: Trading system startup
    print("\nTEST 2: Trading System Startup")
    startup_test = await test_trading_system_startup()
    
    print("\n" + "=" * 50)
    print("TEST RESULTS:")
    print(f"Live Trading Test: {'PASSED' if trading_test else 'FAILED'}")
    print(f"System Startup Test: {'PASSED' if startup_test else 'FAILED'}")
    
    if trading_test and startup_test:
        print("OVERALL RESULT: READY FOR LIVE TRADING")
        return True
    else:
        print("OVERALL RESULT: ISSUES DETECTED")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        if result:
            print("SUCCESS: All tests passed")
            sys.exit(0)
        else:
            print("FAILURE: Some tests failed")
            sys.exit(1)
    except Exception as e:
        print(f"FATAL ERROR: {e}")
        sys.exit(1)
