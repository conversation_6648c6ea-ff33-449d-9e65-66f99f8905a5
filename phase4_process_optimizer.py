#!/usr/bin/env python3
"""
PHASE 4: PROCESS OPTIMIZATION
Optimize running processes for maximum efficiency
"""

import os
import psutil
import time
from datetime import datetime

def get_trading_processes():
    """Get all trading-related Python processes"""
    processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'memory_info', 'cpu_percent', 'create_time']):
        try:
            if proc.info['name'] == 'python.exe' and proc.info['cmdline']:
                cmdline = ' '.join(proc.info['cmdline'])
                if 'main.py' in cmdline:
                    memory_mb = proc.info['memory_info'].rss / 1024 / 1024
                    create_time = datetime.fromtimestamp(proc.info['create_time'])
                    processes.append({
                        'pid': proc.info['pid'],
                        'cmdline': cmdline,
                        'memory_mb': memory_mb,
                        'cpu_percent': proc.info['cpu_percent'] or 0,
                        'create_time': create_time,
                        'process': proc
                    })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return processes

def analyze_processes():
    """Analyze process efficiency and recommend optimization"""
    processes = get_trading_processes()
    
    print("PHASE 4: PROCESS OPTIMIZATION ANALYSIS")
    print("=" * 60)
    print(f"Timestamp: {datetime.now()}")
    print(f"Total trading processes found: {len(processes)}")
    print()
    
    if not processes:
        print("No trading processes found!")
        return
    
    # Sort by memory usage
    processes.sort(key=lambda x: x['memory_mb'], reverse=True)
    
    total_memory = sum(p['memory_mb'] for p in processes)
    total_cpu = sum(p['cpu_percent'] for p in processes)
    
    print("PROCESS ANALYSIS:")
    print(f"  Total Memory Usage: {total_memory:.1f}MB")
    print(f"  Total CPU Usage: {total_cpu:.1f}%")
    print(f"  Average Memory per Process: {total_memory/len(processes):.1f}MB")
    print()
    
    print("TOP PROCESSES BY MEMORY:")
    for i, proc in enumerate(processes[:10], 1):
        age = datetime.now() - proc['create_time']
        print(f"  {i:2d}. PID {proc['pid']:5d}: {proc['memory_mb']:6.1f}MB, {proc['cpu_percent']:5.1f}% CPU, Age: {age}")
    print()
    
    # Identify potential issues
    issues = []
    
    # Too many processes
    if len(processes) > 5:
        issues.append(f"Too many processes ({len(processes)}). Recommend keeping 1-3 main processes.")
    
    # High memory usage processes
    high_memory = [p for p in processes if p['memory_mb'] > 100]
    if high_memory:
        issues.append(f"{len(high_memory)} processes using >100MB memory")
    
    # Low activity processes
    low_activity = [p for p in processes if p['cpu_percent'] < 0.1 and p['memory_mb'] < 20]
    if low_activity:
        issues.append(f"{len(low_activity)} processes with low activity (potential zombies)")
    
    if issues:
        print("OPTIMIZATION RECOMMENDATIONS:")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")
        print()
        
        # Recommend keeping only the most efficient processes
        if len(processes) > 3:
            print("RECOMMENDED ACTION:")
            print("  Keep the top 2-3 most efficient processes:")
            
            # Sort by efficiency (memory usage vs activity)
            efficient_processes = sorted(processes, key=lambda x: x['memory_mb'] / max(x['cpu_percent'], 0.1))[:3]
            
            for i, proc in enumerate(efficient_processes, 1):
                print(f"    {i}. PID {proc['pid']} - {proc['memory_mb']:.1f}MB, {proc['cpu_percent']:.1f}% CPU")
            
            print()
            print("  Consider terminating other processes to optimize performance")
    else:
        print("OPTIMIZATION STATUS: ✅ PROCESSES OPTIMIZED")
    
    print("=" * 60)

def optimize_processes():
    """Optimize processes by keeping only the most efficient ones"""
    processes = get_trading_processes()
    
    if len(processes) <= 3:
        print("Process count is already optimal (≤3 processes)")
        return
    
    print(f"Found {len(processes)} processes. Optimizing to keep top 3...")
    
    # Sort by efficiency (lower memory usage, higher activity is better)
    # But prioritize processes with significant memory (they're likely doing work)
    efficient_processes = []
    working_processes = [p for p in processes if p['memory_mb'] > 50]  # Processes doing significant work
    
    if working_processes:
        # Keep the most efficient working processes
        working_processes.sort(key=lambda x: x['memory_mb'] / max(x['cpu_percent'], 0.1))
        efficient_processes = working_processes[:2]
    
    # Add one more process if needed
    if len(efficient_processes) < 3:
        remaining = [p for p in processes if p not in efficient_processes]
        if remaining:
            remaining.sort(key=lambda x: x['memory_mb'], reverse=True)
            efficient_processes.append(remaining[0])
    
    # Terminate inefficient processes
    to_terminate = [p for p in processes if p not in efficient_processes]
    
    print(f"Keeping {len(efficient_processes)} efficient processes:")
    for proc in efficient_processes:
        print(f"  PID {proc['pid']}: {proc['memory_mb']:.1f}MB, {proc['cpu_percent']:.1f}% CPU")
    
    print(f"\nTerminating {len(to_terminate)} inefficient processes:")
    for proc in to_terminate:
        try:
            print(f"  Terminating PID {proc['pid']}: {proc['memory_mb']:.1f}MB")
            proc['process'].terminate()
            time.sleep(0.5)
            if proc['process'].is_running():
                proc['process'].kill()
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            print(f"    Failed to terminate PID {proc['pid']}: {e}")
    
    print("\nProcess optimization completed!")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--optimize":
        optimize_processes()
    else:
        analyze_processes()
        print("\nTo perform optimization, run: python phase4_process_optimizer.py --optimize")
