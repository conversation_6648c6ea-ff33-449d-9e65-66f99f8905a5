#!/usr/bin/env python3
"""
EMERGENCY CLEANUP - DELETE ALL OBSOLETE ENTRY POINTS
ONLY main.py IS ALLOWED AS ENTRY POINT
"""

import os
import sys

# List of ALL obsolete entry points that must be deleted
OBSOLETE_ENTRY_POINTS = [
    # Main entry point duplicates
    "main_unified_system.py",
    "launch_unified_system.py",
    "system_launcher.py",
    
    # Startup variants  
    "startup_full_system.py",
    "start_enhanced_ai_system.py",
    "system_startup_optimized.py",
    "system_startup_simple.py",
    
    # Run variants
    "run_system.py",
    "run_live_trading.py", 
    "run_main_with_output.py",
    "run_system_with_six.py",
    "install_six_and_run.py",
    
    # Fix and restart variants
    "fix_and_restart_system.py",
    
    # Test startup variants
    "test_startup.py",
    "test_startup_no_emergency.py",
    
    # Other obsolete entry points
    "validate_main_system.py",
    "system_verification.py",
    "validate_live_trading.py",
    "force_live_trading.py",
    "force_trade_execution.py",
    
    # Batch files that could be entry points
    "start.bat",
    "run_bot.bat",
    "start_unified_system.bat",
    "start_unified_system.ps1",
    "start_autonomous_development.bat",
    "start_autonomous_development.ps1",
    "start_autonomous_development.sh",
    "start_profitable_trading.bat",
    
    # Deploy and setup scripts
    "deploy.py",
    "setup_autonomous_development.py",
    
    # Production systems
    "production_safety_system.py",
    
    # Demo and test systems
    "profit_generation_demo.py",
    "test_live_trading_now.py",
    
    # Monitor systems that could be entry points
    "monitor_system_status.py",
    "monitor_live_trading.py",
    "monitor_live_system.py",
    "monitor_system.py",
    "monitor_account_balance.py",
    "real_time_monitor.py",
    
    # System status and reporting
    "system_status.py",
    "system_status_report.py",
    
    # Any other potential entry points
    "comprehensive_trading_diagnostic.py",
    "final_verification.py",
    "autonomous_system_debugger.py",
]

def main():
    """Delete all obsolete entry points"""
    print("=" * 60)
    print("EMERGENCY CLEANUP - DELETING ALL OBSOLETE ENTRY POINTS")
    print("ONLY main.py IS ALLOWED!")
    print("=" * 60)
    
    deleted_count = 0
    not_found_count = 0
    
    for file_path in OBSOLETE_ENTRY_POINTS:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"DELETED: {file_path}")
                deleted_count += 1
            else:
                print(f"NOT FOUND: {file_path}")
                not_found_count += 1
        except Exception as e:
            print(f"ERROR deleting {file_path}: {e}")
    
    print("\n" + "=" * 60)
    print("CLEANUP RESULTS:")
    print(f"Files deleted: {deleted_count}")
    print(f"Files not found: {not_found_count}")
    print("=" * 60)
    
    # Verify only main.py exists
    remaining_main_files = []
    for file in os.listdir("."):
        if file.startswith("main") and file.endswith(".py"):
            remaining_main_files.append(file)
    
    print(f"\nREMAINING MAIN FILES: {remaining_main_files}")
    
    if remaining_main_files == ["main.py"]:
        print("SUCCESS: Only main.py remains as entry point!")
    else:
        print("WARNING: Unexpected main files found!")
    
    print("\n" + "=" * 60)
    print("ENTRY POINT CLEANUP COMPLETED")
    print("ONLY main.py IS NOW THE SINGLE ENTRY POINT")
    print("=" * 60)

if __name__ == "__main__":
    main()
