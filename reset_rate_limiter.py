#!/usr/bin/env python3
"""
Reset Rate Limiter and Restart System
Fixes rate limiting issues and restores normal operation
"""

import asyncio
import sys
import time
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

async def reset_rate_limiter():
    """Reset the rate limiter to normal operation"""
    print("RESETTING RATE LIMITER FOR NORMAL OPERATION...")
    
    try:
        # Import rate limiter
        from bybit_bot.utils.global_rate_limiter import rate_limiter
        
        print(f"Current rate limiter state:")
        print(f"  - Requests per second: {rate_limiter.config.requests_per_second}")
        print(f"  - Emergency mode: {rate_limiter.emergency_mode}")
        print(f"  - Consecutive errors: {rate_limiter.consecutive_errors}")
        
        # Force reset emergency mode
        rate_limiter.reset_emergency_mode()
        
        print(f"\nAfter reset:")
        print(f"  - Requests per second: {rate_limiter.config.requests_per_second}")
        print(f"  - Emergency mode: {rate_limiter.emergency_mode}")
        print(f"  - Consecutive errors: {rate_limiter.consecutive_errors}")
        
        # Test a few acquisitions to ensure it's working
        print(f"\nTesting rate limiter functionality...")
        for i in range(3):
            start_time = time.time()
            await rate_limiter.acquire("normal")
            end_time = time.time()
            print(f"  Test {i+1}: {end_time - start_time:.3f}s delay")
            rate_limiter.report_success()
        
        print(f"\nSUCCESS: Rate limiter reset and functioning normally!")
        print(f"READY FOR SYSTEM RESTART")
        return True
        
    except Exception as e:
        print(f"ERROR resetting rate limiter: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(reset_rate_limiter())
    if success:
        print("\nRATE LIMITER RESET SUCCESSFUL - SYSTEM READY FOR RESTART")
    else:
        print("\nRATE LIMITER RESET FAILED")
    sys.exit(0 if success else 1)
