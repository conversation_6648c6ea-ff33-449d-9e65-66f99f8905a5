#!/usr/bin/env python3
"""
PHASE 4: 24-HOUR SYSTEM VALIDATION MONITOR
Comprehensive monitoring and validation system for bulletproof operation
NO ERRORS, NO WARNINGS, NO BYPASSES, NO BUGS, NO COMPROMISES, NO FALLBACKS
"""

import os
import sys
import time
import json
import sqlite3
import requests
import psutil
import logging
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

@dataclass
class SystemMetrics:
    """System performance and health metrics"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    disk_usage: float
    network_io: Dict[str, int]
    process_count: int
    api_response_time: Optional[float]
    api_status: str
    trading_system_status: str
    error_count: int
    warning_count: int
    profit_generated: float
    trades_executed: int
    uptime_seconds: float

class Phase4SystemMonitor:
    """
    PHASE 4: 24-HOUR SYSTEM VALIDATION MONITOR
    Zero tolerance for errors, warnings, or system degradation
    """
    
    def __init__(self):
        self.start_time = datetime.now()
        self.target_runtime = 24 * 60 * 60  # 24 hours in seconds
        self.api_key = os.getenv('BYBIT_API_KEY')
        self.base_url = 'https://api.bybit.com'
        self.db_path = 'bybit_trading_bot.db'
        self.log_file = f'phase4_monitor_{self.start_time.strftime("%Y%m%d_%H%M%S")}.log'
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s | %(levelname)s | %(message)s',
            handlers=[
                logging.FileHandler(self.log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # Monitoring thresholds
        self.thresholds = {
            'cpu_max': 80.0,
            'memory_max': 85.0,
            'disk_max': 90.0,
            'api_response_max': 5.0,  # seconds
            'error_tolerance': 0,  # ZERO TOLERANCE
            'warning_tolerance': 0,  # ZERO TOLERANCE
            'min_uptime_percent': 99.9
        }
        
        # Performance targets
        self.targets = {
            'daily_profit': 15000.0,
            'hourly_profit': 625.0,
            'minute_profit': 10.42,
            'trades_per_hour': 100,
            'success_rate_min': 70.0
        }
        
        self.metrics_history: List[SystemMetrics] = []
        self.alerts_sent: List[str] = []
        
    def get_system_metrics(self) -> SystemMetrics:
        """Collect comprehensive system metrics"""
        try:
            # System performance
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            network = psutil.net_io_counters()
            
            # API health check
            api_response_time, api_status = self.check_api_health()
            
            # Trading system status
            trading_status = self.check_trading_system_status()
            
            # Error and warning counts
            error_count, warning_count = self.count_log_issues()
            
            # Trading performance
            profit, trades = self.get_trading_performance()
            
            # System uptime
            uptime = (datetime.now() - self.start_time).total_seconds()
            
            return SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                disk_usage=disk.percent,
                network_io={'bytes_sent': network.bytes_sent, 'bytes_recv': network.bytes_recv},
                process_count=len(psutil.pids()),
                api_response_time=api_response_time,
                api_status=api_status,
                trading_system_status=trading_status,
                error_count=error_count,
                warning_count=warning_count,
                profit_generated=profit,
                trades_executed=trades,
                uptime_seconds=uptime
            )
            
        except Exception as e:
            self.logger.error(f"CRITICAL: Failed to collect system metrics: {e}")
            raise
    
    def check_api_health(self) -> tuple[Optional[float], str]:
        """Check Bybit API health and response time"""
        try:
            start_time = time.time()
            response = requests.get(f'{self.base_url}/v5/market/time', timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                return response_time, "HEALTHY"
            elif response.status_code == 403:
                return response_time, "RATE_LIMITED"
            else:
                return response_time, f"ERROR_{response.status_code}"
                
        except Exception as e:
            self.logger.error(f"API health check failed: {e}")
            return None, "UNREACHABLE"
    
    def check_trading_system_status(self) -> str:
        """Check if main trading system is running"""
        try:
            # Check for main.py process
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['cmdline'] and 'main.py' in ' '.join(proc.info['cmdline']):
                        return "RUNNING"
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            return "STOPPED"
        except Exception as e:
            self.logger.error(f"Trading system status check failed: {e}")
            return "UNKNOWN"
    
    def count_log_issues(self) -> tuple[int, int]:
        """Count errors and warnings in recent logs"""
        try:
            error_count = 0
            warning_count = 0
            
            # Check main system logs for recent issues
            if os.path.exists('main.log'):
                with open('main.log', 'r') as f:
                    recent_lines = f.readlines()[-1000:]  # Last 1000 lines
                    for line in recent_lines:
                        if 'ERROR' in line:
                            error_count += 1
                        elif 'WARNING' in line:
                            warning_count += 1
            
            return error_count, warning_count
            
        except Exception as e:
            self.logger.error(f"Log analysis failed: {e}")
            return 0, 0
    
    def get_trading_performance(self) -> tuple[float, int]:
        """Get current trading performance metrics"""
        try:
            if not os.path.exists(self.db_path):
                return 0.0, 0
                
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get total profit
            cursor.execute("SELECT SUM(profit) FROM trades WHERE profit IS NOT NULL")
            profit_result = cursor.fetchone()
            total_profit = profit_result[0] if profit_result[0] else 0.0
            
            # Get trade count
            cursor.execute("SELECT COUNT(*) FROM trades")
            trade_count = cursor.fetchone()[0]
            
            conn.close()
            return total_profit, trade_count
            
        except Exception as e:
            self.logger.error(f"Trading performance check failed: {e}")
            return 0.0, 0
    
    def validate_metrics(self, metrics: SystemMetrics) -> List[str]:
        """Validate metrics against thresholds - ZERO TOLERANCE"""
        violations = []
        
        # System performance violations
        if metrics.cpu_percent > self.thresholds['cpu_max']:
            violations.append(f"CPU usage critical: {metrics.cpu_percent}%")
            
        if metrics.memory_percent > self.thresholds['memory_max']:
            violations.append(f"Memory usage critical: {metrics.memory_percent}%")
            
        if metrics.disk_usage > self.thresholds['disk_max']:
            violations.append(f"Disk usage critical: {metrics.disk_usage}%")
        
        # API performance violations
        if metrics.api_response_time and metrics.api_response_time > self.thresholds['api_response_max']:
            violations.append(f"API response time critical: {metrics.api_response_time}s")
            
        if metrics.api_status not in ['HEALTHY', 'RATE_LIMITED']:
            violations.append(f"API status critical: {metrics.api_status}")
        
        # ZERO TOLERANCE for errors and warnings
        if metrics.error_count > self.thresholds['error_tolerance']:
            violations.append(f"ZERO TOLERANCE VIOLATION: {metrics.error_count} errors detected")
            
        if metrics.warning_count > self.thresholds['warning_tolerance']:
            violations.append(f"ZERO TOLERANCE VIOLATION: {metrics.warning_count} warnings detected")
        
        # Trading system status
        if metrics.trading_system_status != "RUNNING":
            violations.append(f"Trading system not running: {metrics.trading_system_status}")
        
        return violations
    
    def generate_report(self, metrics: SystemMetrics) -> str:
        """Generate comprehensive status report"""
        uptime_hours = metrics.uptime_seconds / 3600
        progress_percent = (metrics.uptime_seconds / self.target_runtime) * 100
        
        report = f"""
================================================================================
PHASE 4: 24-HOUR SYSTEM VALIDATION REPORT
================================================================================
Timestamp: {metrics.timestamp}
Uptime: {uptime_hours:.2f} hours ({progress_percent:.1f}% of 24-hour target)
--------------------------------------------------------------------------------
SYSTEM PERFORMANCE:
  CPU Usage: {metrics.cpu_percent:.1f}%
  Memory Usage: {metrics.memory_percent:.1f}%
  Disk Usage: {metrics.disk_usage:.1f}%
  Process Count: {metrics.process_count}
--------------------------------------------------------------------------------
API STATUS:
  Response Time: {metrics.api_response_time:.3f}s
  Status: {metrics.api_status}
--------------------------------------------------------------------------------
TRADING SYSTEM:
  Status: {metrics.trading_system_status}
  Total Profit: ${metrics.profit_generated:.2f}
  Trades Executed: {metrics.trades_executed}
  Profit/Hour: ${metrics.profit_generated/uptime_hours:.2f}
--------------------------------------------------------------------------------
QUALITY METRICS (ZERO TOLERANCE):
  Errors: {metrics.error_count}
  Warnings: {metrics.warning_count}
--------------------------------------------------------------------------------
TARGET PROGRESS:
  Daily Target: ${self.targets['daily_profit']:.2f}
  Current Achievement: {(metrics.profit_generated/self.targets['daily_profit'])*100:.2f}%
  Hourly Target: ${self.targets['hourly_profit']:.2f}
  Current Rate: ${metrics.profit_generated/uptime_hours:.2f}/hour
================================================================================
"""
        return report
    
    def run_monitoring_cycle(self):
        """Execute one complete monitoring cycle"""
        try:
            # Collect metrics
            metrics = self.get_system_metrics()
            self.metrics_history.append(metrics)
            
            # Validate against thresholds
            violations = self.validate_metrics(metrics)
            
            # Generate and log report
            report = self.generate_report(metrics)
            self.logger.info(report)
            
            # Handle violations
            if violations:
                self.logger.critical("CRITICAL VIOLATIONS DETECTED:")
                for violation in violations:
                    self.logger.critical(f"  - {violation}")
                
                # ZERO TOLERANCE: System must be perfect
                if any("ZERO TOLERANCE" in v for v in violations):
                    self.logger.critical("ZERO TOLERANCE VIOLATION: SYSTEM VALIDATION FAILED")
                    return False
            
            # Check completion
            if metrics.uptime_seconds >= self.target_runtime:
                self.logger.info("SUCCESS: 24-HOUR VALIDATION COMPLETED")
                return True
                
            return None  # Continue monitoring
            
        except Exception as e:
            self.logger.critical(f"CRITICAL: Monitoring cycle failed: {e}")
            return False
    
    def run(self):
        """Main monitoring loop"""
        self.logger.info("STARTING PHASE 4: 24-HOUR SYSTEM VALIDATION")
        self.logger.info(f"Target runtime: {self.target_runtime} seconds (24 hours)")
        self.logger.info("ZERO TOLERANCE: No errors, warnings, or compromises allowed")
        
        try:
            while True:
                result = self.run_monitoring_cycle()
                
                if result is True:
                    self.logger.info("PHASE 4 VALIDATION: COMPLETED SUCCESSFULLY")
                    break
                elif result is False:
                    self.logger.critical("PHASE 4 VALIDATION: FAILED")
                    break
                
                # Wait before next cycle (5 minutes)
                time.sleep(300)
                
        except KeyboardInterrupt:
            self.logger.info("Monitoring stopped by user")
        except Exception as e:
            self.logger.critical(f"CRITICAL: Monitoring system failed: {e}")

if __name__ == "__main__":
    monitor = Phase4SystemMonitor()
    monitor.run()
