#!/usr/bin/env python3
"""
System test to identify actual issues
"""
import sys
import os
from pathlib import Path

def write_result(message):
    """Write results to file since terminal is not working"""
    with open("system_test_results.txt", "a") as f:
        f.write(message + "\n")
        f.flush()

def main():
    # Clear previous results
    if Path("system_test_results.txt").exists():
        Path("system_test_results.txt").unlink()
    
    write_result("SYSTEM TEST STARTING")
    write_result("=" * 40)
    
    # Test 1: Basic Python
    try:
        write_result(f"Python version: {sys.version}")
        write_result("SUCCESS: Python working")
    except Exception as e:
        write_result(f"FAILED: Python issue - {e}")
        return
    
    # Test 2: Environment loading
    try:
        from dotenv import load_dotenv
        load_dotenv()
        write_result("SUCCESS: dotenv loaded")
        
        api_key = os.getenv('BYBIT_API_KEY')
        write_result(f"API key present: {bool(api_key)}")
        
    except Exception as e:
        write_result(f"FAILED: Environment loading - {e}")
    
    # Test 3: Core imports
    try:
        from bybit_bot.core.config import EnhancedBotConfig
        write_result("SUCCESS: EnhancedBotConfig import")
    except Exception as e:
        write_result(f"FAILED: EnhancedBotConfig import - {e}")
        import traceback
        write_result(f"Traceback: {traceback.format_exc()}")
        return
    
    # Test 4: Config creation
    try:
        config = EnhancedBotConfig()
        write_result("SUCCESS: Config created")
        write_result(f"Config type: {type(config)}")
    except Exception as e:
        write_result(f"FAILED: Config creation - {e}")
        import traceback
        write_result(f"Traceback: {traceback.format_exc()}")
        return
    
    # Test 5: Database import
    try:
        from bybit_bot.database.connection import DatabaseManager
        write_result("SUCCESS: DatabaseManager import")
    except Exception as e:
        write_result(f"FAILED: DatabaseManager import - {e}")
        import traceback
        write_result(f"Traceback: {traceback.format_exc()}")
        return
    
    # Test 6: Database creation
    try:
        db_manager = DatabaseManager(config)
        write_result("SUCCESS: DatabaseManager created")
    except Exception as e:
        write_result(f"FAILED: DatabaseManager creation - {e}")
        import traceback
        write_result(f"Traceback: {traceback.format_exc()}")
        return
    
    write_result("CORE TESTS PASSED")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        write_result(f"FATAL ERROR: {e}")
        import traceback
        write_result(f"Traceback: {traceback.format_exc()}")
