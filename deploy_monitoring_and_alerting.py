"""
Deploy Monitoring and Alerting System
Implements real-time monitoring to detect and alert on configuration failures,
API connectivity issues, and trading inactivity to prevent future failures
"""
import asyncio
import logging
import sys
import os
import json
from datetime import datetime, timedelta
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s | %(message)s')
logger = logging.getLogger(__name__)

class TradingBotMonitor:
    """Real-time monitoring system for the trading bot"""
    
    def __init__(self):
        self.monitoring_active = False
        self.last_health_check = datetime.now()
        self.alerts_sent = []
        self.system_status = {
            'api_connectivity': False,
            'database_connection': False,
            'trading_engines': False,
            'profit_generation': False,
            'last_trade_time': None,
            'last_profit_time': None
        }
        
    async def deploy_monitoring_system(self):
        """Deploy the complete monitoring and alerting system"""
        try:
            logger.info("Deploying monitoring and alerting system...")
            
            # Add current directory to path
            sys.path.append(os.path.dirname(os.path.abspath(__file__)))
            
            # Test system health
            logger.info("Testing system health...")
            health_status = await self.perform_health_check()
            
            if health_status['overall_health'] >= 80:
                logger.info("SUCCESS: System health check passed")
                logger.info(f"Overall Health: {health_status['overall_health']:.1f}%")
            else:
                logger.warning(f"WARNING: System health below optimal: {health_status['overall_health']:.1f}%")
            
            # Create monitoring configuration
            logger.info("Creating monitoring configuration...")
            monitoring_config = await self.create_monitoring_config()
            
            # Save monitoring configuration
            config_path = "monitoring_config.json"
            with open(config_path, 'w') as f:
                json.dump(monitoring_config, f, indent=2, default=str)
            logger.info(f"Monitoring configuration saved to {config_path}")
            
            # Create alerting rules
            logger.info("Creating alerting rules...")
            alerting_rules = await self.create_alerting_rules()
            
            # Save alerting rules
            rules_path = "alerting_rules.json"
            with open(rules_path, 'w') as f:
                json.dump(alerting_rules, f, indent=2, default=str)
            logger.info(f"Alerting rules saved to {rules_path}")
            
            # Create monitoring script
            logger.info("Creating monitoring script...")
            await self.create_monitoring_script()
            
            # Test monitoring functionality
            logger.info("Testing monitoring functionality...")
            test_results = await self.test_monitoring_functionality()
            
            if test_results['success']:
                logger.info("SUCCESS: Monitoring functionality test passed")
            else:
                logger.warning("WARNING: Monitoring functionality test failed")
            
            # Create startup monitoring
            logger.info("Creating startup monitoring...")
            await self.create_startup_monitoring()
            
            logger.info("SUCCESS: Monitoring and alerting system deployed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"FAILED: Monitoring deployment failed: {e}")
            return False
    
    async def perform_health_check(self):
        """Perform comprehensive system health check"""
        try:
            health_checks = {
                'configuration_loading': False,
                'api_connectivity': False,
                'database_connection': False,
                'trading_engines': False,
                'ai_systems': False
            }
            
            # Test configuration loading
            try:
                from bybit_bot.core.config import EnhancedBotConfig
                config = EnhancedBotConfig()
                if config and config.api_keys:
                    health_checks['configuration_loading'] = True
                    logger.info("HEALTH CHECK: Configuration loading - PASS")
                else:
                    logger.warning("HEALTH CHECK: Configuration loading - FAIL")
            except Exception as e:
                logger.warning(f"HEALTH CHECK: Configuration loading failed: {e}")
            
            # Test API connectivity
            try:
                from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
                config = EnhancedBotConfig()
                client = EnhancedBybitClient(config)
                await client.initialize()
                
                # Test basic API call
                server_time = await client._get_server_time()
                if server_time:
                    health_checks['api_connectivity'] = True
                    logger.info("HEALTH CHECK: API connectivity - PASS")
                else:
                    logger.warning("HEALTH CHECK: API connectivity - FAIL")
                
                await client.close()
            except Exception as e:
                logger.warning(f"HEALTH CHECK: API connectivity failed: {e}")
            
            # Test database connection
            try:
                from bybit_bot.database.connection import DatabaseManager
                db_manager = DatabaseManager()
                await db_manager.initialize()
                
                # Test basic database operation
                test_result = await db_manager.execute_query("SELECT 1")
                if test_result:
                    health_checks['database_connection'] = True
                    logger.info("HEALTH CHECK: Database connection - PASS")
                else:
                    logger.warning("HEALTH CHECK: Database connection - FAIL")
                
                await db_manager.close()
            except Exception as e:
                logger.warning(f"HEALTH CHECK: Database connection failed: {e}")
            
            # Test trading engines (basic initialization)
            try:
                from main import BybitTradingBotSystem
                trading_bot = BybitTradingBotSystem()
                
                # Check if core components exist
                if hasattr(trading_bot, 'ultra_profit_amplifier') and hasattr(trading_bot, 'risk_manager'):
                    health_checks['trading_engines'] = True
                    logger.info("HEALTH CHECK: Trading engines - PASS")
                else:
                    logger.warning("HEALTH CHECK: Trading engines - FAIL")
            except Exception as e:
                logger.warning(f"HEALTH CHECK: Trading engines failed: {e}")
            
            # Test AI systems availability
            try:
                ai_folder_path = Path("ai")
                if ai_folder_path.exists():
                    ai_files = list(ai_folder_path.glob("*.py"))
                    if len(ai_files) >= 5:  # Expect at least 5 AI modules
                        health_checks['ai_systems'] = True
                        logger.info("HEALTH CHECK: AI systems - PASS")
                    else:
                        logger.warning("HEALTH CHECK: AI systems - FAIL (insufficient modules)")
                else:
                    logger.warning("HEALTH CHECK: AI systems - FAIL (folder not found)")
            except Exception as e:
                logger.warning(f"HEALTH CHECK: AI systems failed: {e}")
            
            # Calculate overall health
            passed_checks = sum(1 for check in health_checks.values() if check)
            total_checks = len(health_checks)
            overall_health = (passed_checks / total_checks) * 100
            
            return {
                'overall_health': overall_health,
                'individual_checks': health_checks,
                'passed_checks': passed_checks,
                'total_checks': total_checks,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                'overall_health': 0,
                'individual_checks': {},
                'passed_checks': 0,
                'total_checks': 0,
                'timestamp': datetime.now(),
                'error': str(e)
            }
    
    async def create_monitoring_config(self):
        """Create comprehensive monitoring configuration"""
        return {
            "monitoring_intervals": {
                "health_check_interval": 300,  # 5 minutes
                "api_connectivity_check": 60,  # 1 minute
                "trading_activity_check": 180,  # 3 minutes
                "profit_verification_check": 600,  # 10 minutes
                "system_resource_check": 120   # 2 minutes
            },
            "alert_thresholds": {
                "max_api_failures": 3,
                "max_trading_inactivity_minutes": 30,
                "min_profit_generation_hourly": 50.0,  # $50/hour minimum
                "max_system_downtime_minutes": 5,
                "min_system_health_percentage": 80
            },
            "alert_channels": {
                "console_logging": True,
                "file_logging": True,
                "email_alerts": False,  # Can be enabled later
                "webhook_alerts": False  # Can be enabled later
            },
            "monitoring_targets": {
                "daily_profit_target": 15000.0,  # $15,000/day
                "hourly_profit_target": 625.0,   # $625/hour
                "minute_profit_target": 10.42,   # $10.42/minute
                "max_acceptable_loss": 500.0,    # $500 max loss
                "min_success_rate": 70.0         # 70% minimum success rate
            },
            "system_components": [
                "configuration_loading",
                "api_connectivity", 
                "database_connection",
                "trading_engines",
                "ai_systems",
                "profit_generation",
                "risk_management"
            ]
        }
    
    async def create_alerting_rules(self):
        """Create comprehensive alerting rules"""
        return {
            "critical_alerts": [
                {
                    "name": "API_CONNECTION_FAILURE",
                    "condition": "api_connectivity == False",
                    "severity": "CRITICAL",
                    "action": "immediate_restart",
                    "message": "CRITICAL: Bybit API connection failed - immediate attention required"
                },
                {
                    "name": "TRADING_SYSTEM_DOWN",
                    "condition": "trading_engines == False",
                    "severity": "CRITICAL", 
                    "action": "immediate_restart",
                    "message": "CRITICAL: Trading engines offline - profit generation stopped"
                },
                {
                    "name": "ZERO_TRADING_ACTIVITY",
                    "condition": "minutes_since_last_trade > 60",
                    "severity": "CRITICAL",
                    "action": "investigate_and_restart",
                    "message": "CRITICAL: No trading activity for over 1 hour - system may be stuck"
                }
            ],
            "warning_alerts": [
                {
                    "name": "LOW_PROFIT_GENERATION",
                    "condition": "hourly_profit < target_hourly_profit * 0.5",
                    "severity": "WARNING",
                    "action": "optimize_strategies",
                    "message": "WARNING: Profit generation below 50% of target"
                },
                {
                    "name": "HIGH_SYSTEM_RESOURCE_USAGE",
                    "condition": "cpu_usage > 80 OR memory_usage > 80",
                    "severity": "WARNING",
                    "action": "monitor_resources",
                    "message": "WARNING: High system resource usage detected"
                },
                {
                    "name": "DATABASE_CONNECTION_SLOW",
                    "condition": "database_response_time > 5000",
                    "severity": "WARNING",
                    "action": "optimize_database",
                    "message": "WARNING: Database response time exceeding 5 seconds"
                }
            ],
            "info_alerts": [
                {
                    "name": "PROFIT_TARGET_ACHIEVED",
                    "condition": "hourly_profit >= target_hourly_profit",
                    "severity": "INFO",
                    "action": "log_success",
                    "message": "INFO: Hourly profit target achieved"
                },
                {
                    "name": "SYSTEM_HEALTH_GOOD",
                    "condition": "overall_health >= 90",
                    "severity": "INFO",
                    "action": "log_status",
                    "message": "INFO: System health excellent"
                }
            ]
        }
    
    async def create_monitoring_script(self):
        """Create the monitoring script file"""
        monitoring_script = '''#!/usr/bin/env python3
"""
Continuous Trading Bot Monitoring Script
Runs continuous monitoring of the trading bot system
"""
import asyncio
import logging
import json
from datetime import datetime, timedelta
from deploy_monitoring_and_alerting import TradingBotMonitor

async def main():
    """Main monitoring loop"""
    monitor = TradingBotMonitor()
    
    print("STARTING CONTINUOUS TRADING BOT MONITORING")
    print("==========================================")
    
    while True:
        try:
            # Perform health check
            health_status = await monitor.perform_health_check()
            
            # Log status
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"[{timestamp}] System Health: {health_status['overall_health']:.1f}%")
            
            # Check for alerts
            if health_status['overall_health'] < 80:
                print(f"[{timestamp}] ALERT: System health below threshold!")
            
            # Wait for next check (5 minutes)
            await asyncio.sleep(300)
            
        except KeyboardInterrupt:
            print("Monitoring stopped by user")
            break
        except Exception as e:
            print(f"Monitoring error: {e}")
            await asyncio.sleep(60)  # Wait 1 minute before retry

if __name__ == "__main__":
    asyncio.run(main())
'''
        
        with open("continuous_monitoring.py", "w") as f:
            f.write(monitoring_script)
        
        logger.info("Monitoring script created: continuous_monitoring.py")
    
    async def test_monitoring_functionality(self):
        """Test the monitoring functionality"""
        try:
            logger.info("Testing monitoring functionality...")
            
            # Test health check
            health_status = await self.perform_health_check()
            if health_status['overall_health'] > 0:
                logger.info("SUCCESS: Health check functionality working")
            else:
                logger.warning("WARNING: Health check functionality issues")
                return {'success': False, 'reason': 'Health check failed'}
            
            # Test configuration loading
            try:
                with open("monitoring_config.json", "r") as f:
                    config = json.load(f)
                if config and 'monitoring_intervals' in config:
                    logger.info("SUCCESS: Monitoring configuration loaded")
                else:
                    logger.warning("WARNING: Monitoring configuration invalid")
                    return {'success': False, 'reason': 'Invalid monitoring config'}
            except Exception as e:
                logger.warning(f"WARNING: Could not load monitoring config: {e}")
                return {'success': False, 'reason': 'Config loading failed'}
            
            # Test alerting rules
            try:
                with open("alerting_rules.json", "r") as f:
                    rules = json.load(f)
                if rules and 'critical_alerts' in rules:
                    logger.info("SUCCESS: Alerting rules loaded")
                else:
                    logger.warning("WARNING: Alerting rules invalid")
                    return {'success': False, 'reason': 'Invalid alerting rules'}
            except Exception as e:
                logger.warning(f"WARNING: Could not load alerting rules: {e}")
                return {'success': False, 'reason': 'Rules loading failed'}
            
            logger.info("SUCCESS: All monitoring functionality tests passed")
            return {'success': True}
            
        except Exception as e:
            logger.error(f"Monitoring functionality test failed: {e}")
            return {'success': False, 'reason': str(e)}
    
    async def create_startup_monitoring(self):
        """Create startup monitoring to prevent future failures"""
        startup_script = '''@echo off
echo STARTING BYBIT TRADING BOT WITH MONITORING
echo ==========================================

echo Activating conda environment...
call conda activate bybit-trader

echo Starting monitoring system...
start /B python continuous_monitoring.py

echo Starting trading bot...
python main.py

echo Trading bot stopped. Monitoring continues...
pause
'''
        
        with open("start_with_monitoring.bat", "w") as f:
            f.write(startup_script)
        
        logger.info("Startup monitoring script created: start_with_monitoring.bat")

async def deploy_monitoring_system():
    """Deploy the complete monitoring and alerting system"""
    monitor = TradingBotMonitor()
    success = await monitor.deploy_monitoring_system()
    return success

if __name__ == "__main__":
    success = asyncio.run(deploy_monitoring_system())
    if success:
        print("Monitoring and alerting system deployment completed successfully!")
        print("Use 'python continuous_monitoring.py' to start monitoring")
        print("Use 'start_with_monitoring.bat' to start bot with monitoring")
    else:
        print("Monitoring and alerting system deployment failed!")
