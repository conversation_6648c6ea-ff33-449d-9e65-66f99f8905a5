#!/usr/bin/env python3
"""
Test configuration loading to identify why config object becomes None
"""

import sys
import traceback
import os
from pathlib import Path

def test_config_loading():
    """Test the configuration loading process step by step"""
    print("TESTING CONFIGURATION LOADING...")
    print("=" * 50)
    
    try:
        # Test 1: Check if .env file exists and can be loaded
        print("TEST 1: Checking .env file...")
        env_file = Path(".env")
        if env_file.exists():
            print("  SUCCESS: .env file exists")
            
            # Load environment variables
            from dotenv import load_dotenv
            load_dotenv()
            
            # Check key environment variables
            bybit_key = os.getenv("BYBIT_API_KEY")
            bybit_secret = os.getenv("BYBIT_API_SECRET")
            
            print(f"  BYBIT_API_KEY: {'SET' if bybit_key else 'NOT SET'}")
            print(f"  BYBIT_API_SECRET: {'SET' if bybit_secret else 'NOT SET'}")
            
            if bybit_key:
                print(f"  API Key length: {len(bybit_key)} characters")
            if bybit_secret:
                print(f"  API Secret length: {len(bybit_secret)} characters")
        else:
            print("  ERROR: .env file not found")
            
    except Exception as e:
        print(f"  ERROR in .env loading: {e}")
        traceback.print_exc()
    
    try:
        # Test 2: Check if config.yaml exists and can be parsed
        print("\nTEST 2: Checking config.yaml file...")
        config_file = Path("config.yaml")
        if config_file.exists():
            print("  SUCCESS: config.yaml file exists")
            
            import yaml
            with open(config_file, 'r') as f:
                config_data = yaml.safe_load(f)
            
            print("  SUCCESS: config.yaml parsed successfully")
            print(f"  Config sections: {list(config_data.keys())}")
            
            # Check API keys in config
            if 'api_keys' in config_data and 'bybit' in config_data['api_keys']:
                bybit_config = config_data['api_keys']['bybit']
                print(f"  Bybit API key in config: {'SET' if bybit_config.get('api_key') else 'NOT SET'}")
                print(f"  Bybit API secret in config: {'SET' if bybit_config.get('api_secret') else 'NOT SET'}")
        else:
            print("  ERROR: config.yaml file not found")
            
    except Exception as e:
        print(f"  ERROR in config.yaml loading: {e}")
        traceback.print_exc()
    
    try:
        # Test 3: Try to import and create EnhancedBotConfig
        print("\nTEST 3: Testing EnhancedBotConfig import...")
        from bybit_bot.core.config import EnhancedBotConfig
        print("  SUCCESS: EnhancedBotConfig imported successfully")
        
        print("\nTEST 4: Testing EnhancedBotConfig initialization...")
        config = EnhancedBotConfig()
        print("  SUCCESS: EnhancedBotConfig created successfully")
        
        # Test config attributes
        print(f"  Config object type: {type(config)}")
        print(f"  Has api_keys attribute: {hasattr(config, 'api_keys')}")
        
        if hasattr(config, 'api_keys'):
            print(f"  API keys object type: {type(config.api_keys)}")
            print(f"  Has bybit attribute: {hasattr(config.api_keys, 'bybit')}")
            
            if hasattr(config.api_keys, 'bybit'):
                bybit_keys = config.api_keys.bybit
                print(f"  Bybit keys type: {type(bybit_keys)}")
                print(f"  Bybit API key: {'SET' if bybit_keys.get('api_key') else 'NOT SET'}")
                print(f"  Bybit API secret: {'SET' if bybit_keys.get('api_secret') else 'NOT SET'}")
                
                if bybit_keys.get('api_key'):
                    print(f"  API Key value: {bybit_keys.get('api_key')}")
                if bybit_keys.get('api_secret'):
                    print(f"  API Secret value: {bybit_keys.get('api_secret')}")
        
        # Test validation
        print("\nTEST 5: Testing configuration validation...")
        if hasattr(config, 'validate_config'):
            errors = config.validate_config()
            if errors:
                print(f"  VALIDATION ERRORS: {errors}")
            else:
                print("  SUCCESS: Configuration validation passed")
        else:
            print("  WARNING: No validate_config method found")
            
    except Exception as e:
        print(f"  ERROR in EnhancedBotConfig: {e}")
        traceback.print_exc()
        return None
    
    print("\n" + "=" * 50)
    print("CONFIGURATION LOADING TEST COMPLETE")
    return config

if __name__ == "__main__":
    config = test_config_loading()
    if config:
        print("SUCCESS: Configuration loaded successfully")
    else:
        print("FAILURE: Configuration loading failed")
        sys.exit(1)
